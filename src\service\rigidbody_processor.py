# -*- coding: utf-8 -*-
"""
刚体处理模块
"""

import numpy as np
from module.MMath import MVector3D, MVector4D, MQuaternion
from module.PmxModel import PmxModel, RigidBody, Joint
from mmd_human_dance_pose.module.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .rigidbody_config import RIGIDBODY_PAIRS
from .vroid_utils import calc_ratio

logger = MLogger(__name__)


class RigidbodyProcessor:
    """刚体处理器"""
    
    def __init__(self):
        pass
    
    def create_body_rigidbody(self, model: PmxModel):
        """创建身体刚体"""
        skin_vidxs = []
        cloth_vidxs = []
        for material_name, vidxs in model.material_vertices.items():
            if "SKIN" in model.materials[material_name].english_name:
                skin_vidxs.extend(vidxs)
            elif "CLOTH" in model.materials[material_name].english_name:
                cloth_vidxs.extend(vidxs)

        bone_vertices = {}
        bone_weights = {}
        for bidx, vidxs in model.vertices.items():
            bone = model.bones[model.bone_indexes[bidx]]
            target_bone_weights = {}

            bone_strong_vidxs = [
                vidx for vidx in vidxs if bone.index in model.vertex_dict[vidx].deform.get_idx_list(0.4)
            ]
            target_bone_vidxs = list(set(skin_vidxs) & set(bone_strong_vidxs))

            if 20 > len(target_bone_vidxs):
                continue

            for vidx in target_bone_vidxs:
                vertex = model.vertex_dict[vidx]
                weight = vertex.deform.get_weight(bone.index)
                if weight > 0:
                    target_bone_weights[vidx] = weight

            if target_bone_weights:
                bone_vertices[bone.name] = target_bone_vidxs
                bone_weights[bone.name] = target_bone_weights

        # 刚体生成
        for rigidbody_name, rigidbody_config in RIGIDBODY_PAIRS.items():
            bone_name = rigidbody_config["bone"]
            
            if bone_name not in model.bones or bone_name not in bone_vertices:
                continue

            bone = model.bones[bone_name]
            vidxs = bone_vertices[bone_name]
            weights = bone_weights[bone_name]

            # 刚体の位置とサイズを計算
            rigidbody_pos, rigidbody_size = self._calc_rigidbody_params(
                model, bone, vidxs, weights, rigidbody_config
            )

            # 刚体作成
            rigidbody = RigidBody(
                name=rigidbody_name,
                english_name=rigidbody_config.get("english", rigidbody_name),
                bone_index=bone.index,
                collision_group=rigidbody_config["group"],
                no_collision_group=sum(1 << g for g in rigidbody_config["no_collision_group"]),
                shape=rigidbody_config["shape"],
                size=rigidbody_size,
                position=rigidbody_pos,
                rotation=MVector3D(),
                mass=1.0,
                linear_damping=0.5,
                angular_damping=0.5,
                restitution=0.0,
                friction=0.5,
                mode=0  # ボーン追従
            )

            model.rigidbodies[rigidbody_name] = rigidbody

        return model

    def _calc_rigidbody_params(self, model, bone, vidxs, weights, config):
        """刚体参数计算"""
        # 頂点の重心を計算
        weighted_positions = []
        total_weight = 0
        
        for vidx in vidxs:
            vertex = model.vertex_dict[vidx]
            weight = weights[vidx]
            weighted_positions.append(vertex.position * weight)
            total_weight += weight
        
        if total_weight == 0:
            center_pos = bone.position
        else:
            center_pos = sum(weighted_positions, MVector3D()) / total_weight

        # 範囲を計算
        min_pos = MVector3D(float('inf'), float('inf'), float('inf'))
        max_pos = MVector3D(float('-inf'), float('-inf'), float('-inf'))
        
        for vidx in vidxs:
            vertex = model.vertex_dict[vidx]
            pos = vertex.position
            
            min_pos.setX(min(min_pos.x, pos.x))
            min_pos.setY(min(min_pos.y, pos.y))
            min_pos.setZ(min(min_pos.z, pos.z))
            
            max_pos.setX(max(max_pos.x, pos.x))
            max_pos.setY(max(max_pos.y, pos.y))
            max_pos.setZ(max(max_pos.z, pos.z))

        # サイズ計算
        size = max_pos - min_pos
        
        # 方向による調整
        direction = config.get("direction", "vertical")
        range_type = config.get("range", "all")
        
        if direction == "horizontal":
            # 水平方向を重視
            size = MVector3D(size.x, size.y * 0.5, size.z)
        elif direction == "vertical":
            # 垂直方向を重視
            size = MVector3D(size.x * 0.5, size.y, size.z * 0.5)
        elif direction == "reverse":
            # 逆方向
            size = MVector3D(size.x * 0.5, size.y * 0.5, size.z)

        # 範囲による調整
        if range_type == "upper":
            center_pos.setY(center_pos.y + size.y * 0.25)
            size.setY(size.y * 0.5)
        elif range_type == "lower":
            center_pos.setY(center_pos.y - size.y * 0.25)
            size.setY(size.y * 0.5)

        # 比率による調整
        if "ratio" in config:
            ratio = config["ratio"]
            size = MVector3D(size.x * ratio.x, size.y * ratio.y, size.z * ratio.z)

        # 最小サイズ制限
        min_size = 0.1
        size.setX(max(size.x, min_size))
        size.setY(max(size.y, min_size))
        size.setZ(max(size.z, min_size))

        return center_pos, size

    def create_joints(self, model: PmxModel):
        """ジョイント作成"""
        # 基本的なジョイント設定
        joint_configs = [
            {
                "name": "首ジョイント",
                "rigidbody_a": "首",
                "rigidbody_b": "頭",
                "position": None,  # 自動計算
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.5, -0.5, -0.5),
                "translation_limit_max": MVector3D(0.5, 0.5, 0.5),
                "rotation_limit_min": MVector3D(-30, -30, -30),
                "rotation_limit_max": MVector3D(30, 30, 30),
            },
            {
                "name": "左肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "左肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
            {
                "name": "右肩ジョイント",
                "rigidbody_a": "上半身3",
                "rigidbody_b": "右肩",
                "position": None,
                "rotation": MVector3D(),
                "translation_limit_min": MVector3D(-0.2, -0.2, -0.2),
                "translation_limit_max": MVector3D(0.2, 0.2, 0.2),
                "rotation_limit_min": MVector3D(-45, -45, -45),
                "rotation_limit_max": MVector3D(45, 45, 45),
            },
        ]

        for joint_config in joint_configs:
            rigidbody_a_name = joint_config["rigidbody_a"]
            rigidbody_b_name = joint_config["rigidbody_b"]
            
            if rigidbody_a_name in model.rigidbodies and rigidbody_b_name in model.rigidbodies:
                rigidbody_a = model.rigidbodies[rigidbody_a_name]
                rigidbody_b = model.rigidbodies[rigidbody_b_name]
                
                # ジョイント位置を自動計算
                if joint_config["position"] is None:
                    joint_position = (rigidbody_a.position + rigidbody_b.position) * 0.5
                else:
                    joint_position = joint_config["position"]

                joint = Joint(
                    name=joint_config["name"],
                    english_name=joint_config["name"],
                    joint_type=0,  # スプリング6DOF
                    rigidbody_index_a=rigidbody_a.index,
                    rigidbody_index_b=rigidbody_b.index,
                    position=joint_position,
                    rotation=joint_config["rotation"],
                    translation_limit_min=joint_config["translation_limit_min"],
                    translation_limit_max=joint_config["translation_limit_max"],
                    rotation_limit_min=joint_config["rotation_limit_min"],
                    rotation_limit_max=joint_config["rotation_limit_max"],
                    spring_constant_translation=MVector3D(1000, 1000, 1000),
                    spring_constant_rotation=MVector3D(100, 100, 100)
                )

                model.joints[joint.name] = joint

        return model
