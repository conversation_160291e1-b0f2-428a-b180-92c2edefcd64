# -*- coding: utf-8 -*-
"""
骨骼处理模块
"""

import os
import numpy as np
from pathlib import Path
from module.MMath import MVector3D, MVector4D, MQuaternion, MMatrix4x4
from mmd.PmxData import PmxModel, Bone, DisplaySlot, Bdef1, Bdef2, Bdef4
from utils.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .bone_config import BONE_PAIRS
from .vroid_constants import DISABLE_BONES

logger = MLogger(__name__)


class BoneProcessor:
    """骨骼处理器"""
    
    def __init__(self):
        pass
    
    def convert_bone(self, model: PmxModel):
        """转换骨骼"""
        if "nodes" not in model.json_data:
            logger.error("変換可能なボーン情報がないため、処理を中断します。", decoration=MLogger.DECORATION_BOX)
            return None, None

        # 表示枠 ------------------------
        model.display_slots["全ての親"] = DisplaySlot("Root", "Root", 1, 1)
        model.display_slots["全ての親"].references.append((0, 0))

        # モーフの表示枠
        model.display_slots["表情"] = DisplaySlot("表情", "Exp", 1, 1)

        node_dict = {}
        node_name_dict = {}
        for nidx, node in enumerate(model.json_data["nodes"]):
            node_translation = MVector3D() if "translation" not in node else MVector3D(*node["translation"])
            node_rotation = MQuaternion() if "rotation" not in node else MQuaternion(*node["rotation"])
            node_scale = MVector3D(1, 1, 1) if "scale" not in node else MVector3D(*node["scale"])

            node_dict[nidx] = {
                "name": node["name"] if "name" in node else f"node_{nidx}",
                "parent": -1,
                "children": [],
                "relative_position": node_translation,
                "rotation": node_rotation,
                "scale": node_scale,
                "absolute_position": MVector3D(),
            }
            node_name_dict[node_dict[nidx]["name"]] = nidx

            if "children" in node:
                node_dict[nidx]["children"] = node["children"]
                for cidx in node["children"]:
                    if cidx in node_dict:
                        node_dict[cidx]["parent"] = nidx

        # 絶対位置を計算
        for nidx in node_dict.keys():
            node_dict[nidx]["absolute_position"] = self.calc_bone_position(model, node_dict, node_dict[nidx])

        # ボーン生成
        bone_name_dict = {}
        for bone_name, bone_param in BONE_PAIRS.items():
            if bone_name in node_name_dict:
                node_idx = node_name_dict[bone_name]
                node_param = node_dict[node_idx]

                bone = Bone(
                    name=bone_param["name"],
                    english_name=bone_name,
                    position=node_param["absolute_position"],
                    parent_index=-1,
                    layer=0,
                    flag=bone_param["flag"],
                )

                # 親ボーンの設定
                if bone_param["parent"] and bone_param["parent"] in BONE_PAIRS:
                    parent_bone_name = BONE_PAIRS[bone_param["parent"]]["name"]
                    if parent_bone_name in model.bones:
                        bone.parent_index = model.bones[parent_bone_name].index

                # 表示先の設定
                if bone_param["tail"]:
                    if isinstance(bone_param["tail"], str):
                        if bone_param["tail"] in BONE_PAIRS:
                            tail_bone_name = BONE_PAIRS[bone_param["tail"]]["name"]
                            if tail_bone_name in model.bones:
                                bone.tail_index = model.bones[tail_bone_name].index
                            else:
                                bone.tail_position = MVector3D(0, 0, 1)
                        else:
                            bone.tail_position = MVector3D(0, 0, 1)
                    elif isinstance(bone_param["tail"], MVector3D):
                        bone.tail_position = bone_param["tail"]
                    else:
                        bone.tail_position = MVector3D(0, 0, 1)
                else:
                    bone.tail_position = MVector3D(0, 0, 1)

                model.bones[bone.name] = bone
                bone_name_dict[node_idx] = bone.name

                # 表示枠への追加
                if bone_param["display"]:
                    if bone_param["display"] not in model.display_slots:
                        model.display_slots[bone_param["display"]] = DisplaySlot(
                            bone_param["display"], bone_param["display"], 1, 1
                        )
                    model.display_slots[bone_param["display"]].references.append((0, bone.index))

        return model, bone_name_dict

    def reconvert_bone(self, model: PmxModel):
        """重新转换骨骼位置"""
        # 指先端の位置を計算して配置
        finger_dict = {
            "左親指２": {"vertices": [], "direction": -1, "edge_name": "左親指先"},
            "左人指３": {"vertices": [], "direction": -1, "edge_name": "左人指先"},
            "左中指３": {"vertices": [], "direction": -1, "edge_name": "左中指先"},
            "左薬指３": {"vertices": [], "direction": -1, "edge_name": "左薬指先"},
            "左小指３": {"vertices": [], "direction": -1, "edge_name": "左小指先"},
            "右親指２": {"vertices": [], "direction": 1, "edge_name": "右親指先"},
            "右人指３": {"vertices": [], "direction": 1, "edge_name": "右人指先"},
            "右中指３": {"vertices": [], "direction": 1, "edge_name": "右中指先"},
            "右薬指３": {"vertices": [], "direction": 1, "edge_name": "右薬指先"},
            "右小指３": {"vertices": [], "direction": 1, "edge_name": "右小指先"},
        }
        
        # つま先の位置を計算して配置
        toe_dict = {
            "左足首": {"vertices": [], "direction": MVector3D(0, 0, -1), "edge_name": "左つま先"},
            "右足首": {"vertices": [], "direction": MVector3D(0, 0, -1), "edge_name": "右つま先"},
        }

        # 各頂点について、ウェイトを持つボーンを調べる
        # model.vertices.values()は頂点リストのリストなので、フラット化する
        for vertex_list in model.vertices.values():
            for vertex in vertex_list:
                if type(vertex.deform) is Bdef1:
                    bone_indices = [vertex.deform.index0]
                elif type(vertex.deform) is Bdef2:
                    bone_indices = [vertex.deform.index0, vertex.deform.index1]
                elif type(vertex.deform) is Bdef4:
                    bone_indices = [
                        vertex.deform.index0,
                        vertex.deform.index1,
                        vertex.deform.index2,
                        vertex.deform.index3,
                    ]
                else:
                    continue

                for bone_idx in bone_indices:
                    if bone_idx < len(model.bones):
                        bone_name = list(model.bones.keys())[bone_idx]

                        # 指の先端ボーン処理
                        if bone_name in finger_dict:
                            finger_dict[bone_name]["vertices"].append(vertex.position)

                        # つま先ボーン処理
                        if bone_name in toe_dict:
                            toe_dict[bone_name]["vertices"].append(vertex.position)

        # 指先端ボーンの位置を計算
        for bone_name, finger_info in finger_dict.items():
            if finger_info["vertices"] and bone_name in model.bones:
                # 最も遠い頂点を見つける
                base_bone = model.bones[bone_name]
                max_distance = 0
                farthest_pos = base_bone.position
                
                for vertex_pos in finger_info["vertices"]:
                    distance = (vertex_pos - base_bone.position).length()
                    if distance > max_distance:
                        max_distance = distance
                        farthest_pos = vertex_pos
                
                # 先端ボーンの位置を設定
                edge_name = finger_info["edge_name"]
                if edge_name in model.bones:
                    direction_vec = (farthest_pos - base_bone.position).normalized()
                    model.bones[edge_name].position = base_bone.position + direction_vec * max_distance * 1.1

        # つま先ボーンの位置を計算
        for bone_name, toe_info in toe_dict.items():
            if toe_info["vertices"] and bone_name in model.bones:
                base_bone = model.bones[bone_name]
                
                # Z軸方向で最も前の頂点を見つける
                min_z = float('inf')
                front_pos = base_bone.position
                
                for vertex_pos in toe_info["vertices"]:
                    if vertex_pos.z < min_z:
                        min_z = vertex_pos.z
                        front_pos = vertex_pos
                
                # つま先ボーンの位置を設定
                edge_name = toe_info["edge_name"]
                if edge_name in model.bones:
                    model.bones[edge_name].position = MVector3D(base_bone.position.x, front_pos.y, front_pos.z)

        return model

    def calc_bone_position(self, model: PmxModel, node_dict: dict, node_param: dict):
        """骨骼位置计算"""
        if node_param["parent"] == -1:
            return node_param["relative_position"]

        return node_param["relative_position"] + self.calc_bone_position(
            model, node_dict, node_dict[node_param["parent"]]
        )
