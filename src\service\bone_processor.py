# -*- coding: utf-8 -*-
"""
骨骼处理模块 - 修复版本
"""

import os
import math
import numpy as np
from pathlib import Path
from module.MMath import MVector3D, MVector4D, MQuaternion, MMatrix4x4
from mmd.PmxData import PmxModel, Bone, DisplaySlot, Bdef1, Bdef2, Bdef4, Ik, IkLink
from utils.MLogger import <PERSON><PERSON>ogger
from .bone_config import BONE_PAIRS
from .vroid_constants import DISABLE_BONES

logger = MLogger(__name__)


class BoneProcessor:
    """骨骼处理器"""
    
    def __init__(self):
        pass
    
    def convert_bone(self, model: PmxModel):
        """转换骨骼 - 完整版本"""
        if "nodes" not in model.json_data:
            logger.error("変換可能なボーン情報がないため、処理を中断します。", decoration=MLogger.DECORATION_BOX)
            return None, None

        # 表示枠 ------------------------
        model.display_slots["全ての親"] = DisplaySlot("Root", "Root", 1, 1)
        model.display_slots["全ての親"].references.append((0, 0))

        # モーフの表示枠
        model.display_slots["表情"] = DisplaySlot("表情", "Exp", 1, 1)

        node_dict = {}
        node_name_dict = {}
        for nidx, node in enumerate(model.json_data["nodes"]):
            node_translation = MVector3D() if "translation" not in node else MVector3D(*node["translation"])
            node_rotation = MQuaternion() if "rotation" not in node else MQuaternion(*node["rotation"])
            node_scale = MVector3D(1, 1, 1) if "scale" not in node else MVector3D(*node["scale"])

            node_dict[nidx] = {
                "name": node["name"] if "name" in node else f"node_{nidx}",
                "parent": -1,
                "children": [],
                "relative_position": node_translation,
                "rotation": node_rotation,
                "scale": node_scale,
                "position": MVector3D(),
            }
            node_name_dict[node_dict[nidx]["name"]] = nidx

        # 親子関係を設定
        for nidx, node in enumerate(model.json_data["nodes"]):
            if "children" in node:
                for child_idx in node["children"]:
                    node_dict[child_idx]["parent"] = nidx
                    node_dict[nidx]["children"].append(child_idx)

        # 絶対位置計算
        for nidx, node_param in node_dict.items():
            node_dict[nidx]["position"] = self.calc_bone_position(model, node_dict, node_param)

        # まずは人体ボーン
        bone_name_dict = {}
        for node_name, bone_param in BONE_PAIRS.items():
            parent_name = BONE_PAIRS[bone_param["parent"]]["name"] if bone_param["parent"] else None
            parent_index = model.bones[parent_name].index if parent_name else -1

            node_index = -1
            position = MVector3D()
            bone = Bone(bone_param["name"], node_name, position, parent_index, 0, bone_param["flag"])
            if bone.name in model.bones:
                # 同一名が既にある場合、スルー（尻対策）
                continue

            if node_name in node_name_dict:
                node_index = node_name_dict[node_name]
                position = node_dict[node_index]["position"]

            bone.position = position
            bone.index = len(model.bones)

            # 表示枠
            if bone_param["display"]:
                if bone_param["display"] not in model.display_slots:
                    model.display_slots[bone_param["display"]] = DisplaySlot(
                        bone_param["display"], bone_param["display"], 0, 0
                    )
                model.display_slots[bone_param["display"]].references.append((0, bone.index))

            model.bones[bone.name] = bone
            bone_name_dict[node_name] = {
                "index": bone.index,
                "name": bone.name,
                "node_name": node_name,
                "node_index": node_index,
            }

        if "髪" not in model.display_slots:
            model.display_slots["髪"] = DisplaySlot("髪", "Hair", 0, 0)
        if "その他" not in model.display_slots:
            model.display_slots["その他"] = DisplaySlot("その他", "Other", 0, 0)

        # 人体以外のボーン
        hair_blocks = []
        other_blocks = []
        for nidx, node_param in node_dict.items():
            if node_param["name"] not in bone_name_dict:
                bone = Bone(node_param["name"], node_param["name"], node_param["position"], -1, 0, 0x0002)
                parent_index = (
                    bone_name_dict[node_dict[node_param["parent"]]["name"]]["index"]
                    if node_param["parent"] in node_dict and node_dict[node_param["parent"]]["name"] in bone_name_dict
                    else -1
                )
                bone.parent_index = parent_index
                bone.index = len(model.bones)

                if node_param["name"] not in DISABLE_BONES:
                    # 1.4.1でボーン名が短くなったのでそれに合わせて調整
                    node_names = (
                        node_param["name"].split("_")
                        if "Hair" in node_param["name"]
                        else node_param["name"].split("_Sec_")
                        if "_Sec_" in node_param["name"]
                        else node_param["name"].split("J_")
                    )
                    bone_block = None
                    bone_name = None

                    if "Hair" in node_param["name"]:
                        if len(hair_blocks) == 0:
                            bone_block = {"bone_block_name": "髪", "bone_block_size": 1, "size": 1}
                        else:
                            bone_block = {
                                "bone_block_name": "髪",
                                "bone_block_size": hair_blocks[-1]["bone_block_size"],
                                "size": hair_blocks[-1]["size"] + 1,
                            }
                        hair_blocks.append(bone_block)
                    else:
                        if len(other_blocks) == 0:
                            bone_block = {"bone_block_name": "装飾", "bone_block_size": 1, "size": 1}
                        else:
                            bone_block = {
                                "bone_block_name": "装飾",
                                "bone_block_size": other_blocks[-1]["bone_block_size"],
                                "size": other_blocks[-1]["size"] + 1,
                            }
                        other_blocks.append(bone_block)
                    bone_name = (
                        f'{bone_block["bone_block_name"]}_{bone_block["bone_block_size"]:02d}-{bone_block["size"]:02d}'
                    )

                    if "Hair" not in node_param["name"] and len(node_names) > 1:
                        # 装飾の場合、末尾を入れる
                        bone_name += node_param["name"][len(node_names[0]) :]

                    bone.name = bone_name

                model.bones[bone.name] = bone
                bone_name_dict[node_param["name"]] = {
                    "index": bone.index,
                    "name": bone.name,
                    "node_name": node_param["name"],
                    "node_index": node_name_dict[node_param["name"]],
                }

                if node_param["name"] not in DISABLE_BONES:
                    if len(node_param["children"]) == 0:
                        # 末端の場合次ボーンで段を変える(加算用にsizeは0)
                        if "Hair" in node_param["name"]:
                            hair_blocks.append(
                                {
                                    "bone_block_name": "髪",
                                    "bone_block_size": hair_blocks[-1]["bone_block_size"] + 1,
                                    "size": 0,
                                }
                            )
                        else:
                            other_blocks.append(
                                {
                                    "bone_block_name": "装飾",
                                    "bone_block_size": other_blocks[-1]["bone_block_size"] + 1,
                                    "size": 0,
                                }
                            )

        # ローカル軸・IK設定 - 这是关键的缺失部分！
        for bone in model.bones.values():
            model.bone_indexes[bone.index] = bone.name

            # 人体ボーン
            if bone.english_name in BONE_PAIRS:
                # 表示先
                tail = BONE_PAIRS[bone.english_name]["tail"]
                if tail:
                    if type(tail) is MVector3D:
                        bone.tail_position = tail.copy()
                    else:
                        bone.tail_index = bone_name_dict[tail]["index"]
                if bone.name == "下半身":
                    # 腰は表示順が上なので、相対指定
                    bone.tail_position = model.bones["腰"].position - bone.position
                elif bone.name == "頭":
                    bone.tail_position = MVector3D(0, 1, 0)

                direction = bone.name[0]

                # 足IK
                leg_name = f"{direction}足"
                knee_name = f"{direction}ひざ"
                ankle_name = f"{direction}足首"
                toe_name = f"{direction}つま先"

                if (
                    bone.name in ["右足ＩＫ", "左足ＩＫ"]
                    and leg_name in model.bones
                    and knee_name in model.bones
                    and ankle_name in model.bones
                ):
                    leg_ik_link = []
                    leg_ik_link.append(
                        IkLink(
                            model.bones[knee_name].index,
                            1,
                            MVector3D(math.radians(-180), 0, 0),
                            MVector3D(math.radians(-0.5), 0, 0),
                        )
                    )
                    leg_ik_link.append(IkLink(model.bones[leg_name].index, 0))
                    leg_ik = Ik(model.bones[ankle_name].index, 40, 1, leg_ik_link)
                    bone.ik = leg_ik

                if bone.name in ["右つま先ＩＫ", "左つま先ＩＫ"] and ankle_name in model.bones and toe_name in model.bones:
                    toe_ik_link = []
                    toe_ik_link.append(IkLink(model.bones[ankle_name].index, 0))
                    toe_ik = Ik(model.bones[toe_name].index, 40, 1, toe_ik_link)
                    bone.ik = toe_ik

                if bone.name in ["上半身3"] and "上半身2" in model.bones:
                    bone.flag |= 0x0100
                    bone.effect_index = model.bones["上半身2"].index
                    bone.effect_factor = 0.4

                if bone.name in ["右目", "左目"] and "両目" in model.bones:
                    bone.flag |= 0x0100
                    bone.effect_index = model.bones["両目"].index
                    bone.effect_factor = 0.3

                if bone.name in ["両目光"] and "両目" in model.bones:
                    bone.flag |= 0x0100
                    bone.effect_index = model.bones["両目"].index
                    bone.effect_factor = 1

                if bone.name in ["右目光", "左目光"] and "両目光" in model.bones:
                    bone.flag |= 0x0100
                    bone.effect_index = model.bones["両目光"].index
                    bone.effect_factor = 0.3
            else:
                # 人体以外
                # 表示先
                node_param = node_dict[node_name_dict[bone.english_name]]
                tail_index = (
                    bone_name_dict[node_dict[node_param["children"][0]]["name"]]["index"]
                    if node_param["children"]
                    and node_param["children"][0] in node_dict
                    and node_dict[node_param["children"][0]]["name"] in bone_name_dict
                    else -1
                )
                if tail_index >= 0:
                    bone.tail_index = tail_index
                    bone.flag |= 0x0001 | 0x0008 | 0x0010
                elif "Glasses" in bone.name:
                    # メガネは単体で操作可能(+移動)
                    bone.flag |= 0x0004 | 0x0008 | 0x0010

                if bone.getVisibleFlag():
                    if "Hair" in bone.english_name:
                        model.display_slots["髪"].references.append((0, bone.index))
                    else:
                        model.display_slots["その他"].references.append((0, bone.index))

        logger.info("-- ボーンデータ解析終了")

        return model, bone_name_dict

    def reconvert_bone(self, model: PmxModel):
        """重新转换骨骼位置"""
        # 指先端の位置を計算して配置
        finger_dict = {
            "左親指２": {"vertices": [], "direction": -1, "edge_name": "左親指先"},
            "左人指３": {"vertices": [], "direction": -1, "edge_name": "左人指先"},
            "左中指３": {"vertices": [], "direction": -1, "edge_name": "左中指先"},
            "左薬指３": {"vertices": [], "direction": -1, "edge_name": "左薬指先"},
            "左小指３": {"vertices": [], "direction": -1, "edge_name": "左小指先"},
            "右親指２": {"vertices": [], "direction": 1, "edge_name": "右親指先"},
            "右人指３": {"vertices": [], "direction": 1, "edge_name": "右人指先"},
            "右中指３": {"vertices": [], "direction": 1, "edge_name": "右中指先"},
            "右薬指３": {"vertices": [], "direction": 1, "edge_name": "右薬指先"},
            "右小指３": {"vertices": [], "direction": 1, "edge_name": "右小指先"},
        }

        # つま先の位置を計算して配置
        toe_dict = {
            "左足首": {"vertices": [], "direction": MVector3D(0, 0, -1), "edge_name": "左つま先"},
            "右足首": {"vertices": [], "direction": MVector3D(0, 0, -1), "edge_name": "右つま先"},
        }

        # 各頂点について、ウェイトを持つボーンを調べる
        # model.vertices.values()は頂点リストのリストなので、フラット化する
        for vertex_list in model.vertices.values():
            for vertex_idx in vertex_list:
                vertex = model.vertex_dict[vertex_idx]
                if type(vertex.deform) is Bdef1:
                    bone_indices = [vertex.deform.index0]
                elif type(vertex.deform) is Bdef2:
                    bone_indices = [vertex.deform.index0, vertex.deform.index1]
                elif type(vertex.deform) is Bdef4:
                    bone_indices = [
                        vertex.deform.index0,
                        vertex.deform.index1,
                        vertex.deform.index2,
                        vertex.deform.index3,
                    ]
                else:
                    continue

                for bone_idx in bone_indices:
                    if bone_idx in model.bone_indexes:
                        bone_name = model.bone_indexes[bone_idx]

                        # 指の先端ボーン処理
                        if bone_name in finger_dict:
                            finger_dict[bone_name]["vertices"].append(vertex.position)

                        # つま先ボーン処理
                        if bone_name in toe_dict:
                            toe_dict[bone_name]["vertices"].append(vertex.position)

        # 指先端ボーンの位置を計算
        for bone_name, finger_info in finger_dict.items():
            if finger_info["vertices"] and bone_name in model.bones:
                # 最も遠い頂点を見つける
                base_bone = model.bones[bone_name]
                max_distance = 0
                farthest_pos = base_bone.position

                for vertex_pos in finger_info["vertices"]:
                    distance = (vertex_pos - base_bone.position).length()
                    if distance > max_distance:
                        max_distance = distance
                        farthest_pos = vertex_pos

                # 先端ボーンの位置を設定
                edge_name = finger_info["edge_name"]
                if edge_name in model.bones:
                    direction_vec = (farthest_pos - base_bone.position).normalized()
                    model.bones[edge_name].position = base_bone.position + direction_vec * max_distance * 1.1

        # つま先ボーンの位置を計算
        for bone_name, toe_info in toe_dict.items():
            if toe_info["vertices"] and bone_name in model.bones:
                base_bone = model.bones[bone_name]

                # Z軸方向で最も前の頂点を見つける
                min_z = float('inf')
                front_pos = base_bone.position

                for vertex_pos in toe_info["vertices"]:
                    if vertex_pos.z < min_z:
                        min_z = vertex_pos.z
                        front_pos = vertex_pos

                # つま先ボーンの位置を設定
                edge_name = toe_info["edge_name"]
                if edge_name in model.bones:
                    model.bones[edge_name].position = MVector3D(base_bone.position.x, front_pos.y, front_pos.z)

        return model

    def calc_bone_position(self, model: PmxModel, node_dict: dict, node_param: dict):
        """骨骼位置计算"""
        if node_param["parent"] == -1:
            return node_param["relative_position"]

        return node_param["relative_position"] + self.calc_bone_position(
            model, node_dict, node_dict[node_param["parent"]]
        )
