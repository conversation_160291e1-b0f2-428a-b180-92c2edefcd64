# -*- coding: utf-8 -*-
"""
变形处理模块
"""

import copy
import numpy as np
from module.MMath import MVector3D, MVector4D, MQuaternion
from module.PmxModel import PmxModel, VertexMorph, BoneMorph, GroupMorph
from module.PmxModel import VertexMorphOffset, BoneMorphOffset, GroupMorphOffset
from mmd_human_dance_pose.module.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .morph_config import MORPH_PAIRS
from .vroid_constants import MORPH_SYSTEM, MORPH_EYEBROW, MORPH_EYE, MORPH_LIP, MORPH_OTHER, DEFINED_MORPH_NAMES

logger = MLogger(__name__)


class MorphProcessor:
    """变形处理器"""
    
    def __init__(self):
        pass
    
    def convert_morph(self, model: PmxModel, is_vroid1: bool):
        """转换变形"""
        # グループモーフ定義
        if (
            "extensions" not in model.json_data
            or "VRM" not in model.json_data["extensions"]
            or "blendShapeMaster" not in model.json_data["extensions"]["VRM"]
            or "blendShapeGroups" not in model.json_data["extensions"]["VRM"]["blendShapeMaster"]
        ):
            return model

        # 一旦置き換えて、既存はクリア
        vertex_morphs = copy.deepcopy(model.org_morphs)
        target_morphs = copy.deepcopy(model.org_morphs)
        model.org_morphs = {}

        logger.info("-- -- モーフ調整準備")

        face_close_dict = {}
        if is_vroid1:
            for base_offset in target_morphs["Fcl_EYE_Close"].offsets:
                face_close_dict[base_offset.vertex_index] = base_offset.position_offset.copy().data()

        # VRMのブレンドシェイプグループを処理
        blend_shape_groups = model.json_data["extensions"]["VRM"]["blendShapeMaster"]["blendShapeGroups"]
        
        for group in blend_shape_groups:
            group_name = group["name"]
            
            if group_name in MORPH_PAIRS:
                morph_config = MORPH_PAIRS[group_name]
                morph_name = morph_config["name"]
                panel = morph_config["panel"]
                
                # 基本的なモーフ作成
                if "binds" in morph_config:
                    # グループモーフ
                    group_morph = GroupMorph(
                        name=morph_name,
                        english_name=group_name,
                        panel=panel,
                        offsets=[]
                    )
                    
                    bind_names = morph_config["binds"]
                    ratios = morph_config.get("ratios", [1.0] * len(bind_names))
                    
                    for i, bind_name in enumerate(bind_names):
                        if bind_name in MORPH_PAIRS:
                            bind_morph_name = MORPH_PAIRS[bind_name]["name"]
                            ratio = ratios[i] if i < len(ratios) else 1.0
                            
                            # モーフが存在するかチェック
                            if bind_morph_name in model.morphs:
                                morph_idx = list(model.morphs.keys()).index(bind_morph_name)
                                offset = GroupMorphOffset(morph_idx, ratio)
                                group_morph.offsets.append(offset)
                    
                    if group_morph.offsets:
                        model.morphs[morph_name] = group_morph
                
                elif "bone" in morph_config:
                    # ボーンモーフ
                    bone_morph = BoneMorph(
                        name=morph_name,
                        english_name=group_name,
                        panel=panel,
                        offsets=[]
                    )
                    
                    bone_names = morph_config["bone"]
                    move_ratios = morph_config.get("move_ratios", [MVector3D()] * len(bone_names))
                    rotate_ratios = morph_config.get("rotate_ratios", [MQuaternion()] * len(bone_names))
                    
                    for i, bone_name in enumerate(bone_names):
                        if bone_name in model.bones:
                            bone_idx = list(model.bones.keys()).index(bone_name)
                            move_ratio = move_ratios[i] if i < len(move_ratios) else MVector3D()
                            rotate_ratio = rotate_ratios[i] if i < len(rotate_ratios) else MQuaternion()
                            
                            offset = BoneMorphOffset(bone_idx, move_ratio, rotate_ratio)
                            bone_morph.offsets.append(offset)
                    
                    if bone_morph.offsets:
                        model.morphs[morph_name] = bone_morph
                
                elif "creates" in morph_config:
                    # 新規作成が必要なモーフ
                    creates = morph_config["creates"]
                    vertex_morph = self._create_vertex_morph(
                        model, morph_name, group_name, panel, creates, vertex_morphs
                    )
                    if vertex_morph:
                        model.morphs[morph_name] = vertex_morph
                
                elif "split" in morph_config:
                    # 分割モーフ
                    split_base = morph_config["split"]
                    if split_base in vertex_morphs:
                        vertex_morph = self._create_split_morph(
                            model, morph_name, group_name, panel, split_base, vertex_morphs, group_name.endswith("_L")
                        )
                        if vertex_morph:
                            model.morphs[morph_name] = vertex_morph
                
                else:
                    # 通常の頂点モーフ
                    if group_name in vertex_morphs:
                        vertex_morph = VertexMorph(
                            name=morph_name,
                            english_name=group_name,
                            panel=panel,
                            offsets=vertex_morphs[group_name].offsets
                        )
                        model.morphs[morph_name] = vertex_morph

        # 表示枠への追加
        for morph_name, morph in model.morphs.items():
            if morph.panel != MORPH_SYSTEM:
                model.display_slots["表情"].references.append((2, morph.index))

        return model

    def _create_vertex_morph(self, model, morph_name, english_name, panel, creates, vertex_morphs):
        """頂点モーフを作成"""
        vertex_morph = VertexMorph(
            name=morph_name,
            english_name=english_name,
            panel=panel,
            offsets=[]
        )
        
        # 指定されたメッシュ部分から頂点を抽出
        for create_target in creates:
            # メッシュ名に基づいて頂点を検索
            for vertex_idx, vertex in model.vertices.items():
                # ここでは簡単な実装として、すべての頂点を対象とする
                # 実際の実装では、メッシュ名やマテリアル名に基づいて頂点を絞り込む
                if self._is_target_vertex(vertex, create_target):
                    offset = VertexMorphOffset(vertex_idx, MVector3D())
                    vertex_morph.offsets.append(offset)
        
        return vertex_morph if vertex_morph.offsets else None

    def _create_split_morph(self, model, morph_name, english_name, panel, split_base, vertex_morphs, is_left):
        """分割モーフを作成"""
        if split_base not in vertex_morphs:
            return None
        
        base_morph = vertex_morphs[split_base]
        vertex_morph = VertexMorph(
            name=morph_name,
            english_name=english_name,
            panel=panel,
            offsets=[]
        )
        
        # 左右の判定に基づいて頂点を分割
        for offset in base_morph.offsets:
            vertex_idx = offset.vertex_index
            if vertex_idx in model.vertices:
                vertex = model.vertices[vertex_idx]
                
                # X座標に基づいて左右を判定
                if is_left and vertex.position.x > 0:
                    vertex_morph.offsets.append(offset)
                elif not is_left and vertex.position.x < 0:
                    vertex_morph.offsets.append(offset)
        
        return vertex_morph if vertex_morph.offsets else None

    def _is_target_vertex(self, vertex, target_name):
        """頂点が対象かどうかを判定"""
        # 簡単な実装として、常にTrueを返す
        # 実際の実装では、頂点の位置やマテリアル情報に基づいて判定
        return True
