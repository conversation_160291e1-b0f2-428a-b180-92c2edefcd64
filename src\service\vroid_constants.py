# -*- coding: utf-8 -*-
"""
VRoid转换相关的常量定义
"""

from module.MMath import MVector3D, MQuaternion

# MIME类型映射
MIME_TYPE = {
    "image/png": "png",
    "image/jpeg": "jpg",
    "image/ktx": "ktx",
    "image/ktx2": "ktx2",
    "image/webp": "webp",
    "image/vnd-ms.dds": "dds",
    "audio/wav": "wav",
}

# MMDにおける1cm＝0.125(ミクセル)、1m＝12.5
MIKU_METER = 12.5

# 禁用的骨骼名称
DISABLE_BONES = [
    "Face",
    "Body",
    "Hair",
    "Hairs",
    "Hair001",
    "secondary",
]

# 变形类型常量
MORPH_SYSTEM = 0
MORPH_EYEBROW = 1
MORPH_EYE = 2
MORPH_LIP = 3
MORPH_OTHER = 4

# 预定义的变形名称
DEFINED_MORPH_NAMES = [
    "Neutral",
    "A",
    "I",
    "U",
    "E",
    "O",
    "Blink",
    "Blink_L",
    "Blink_R",
    "Angry",
    "Fun",
    "Joy",
    "Sorrow",
    "Surprised",
]
