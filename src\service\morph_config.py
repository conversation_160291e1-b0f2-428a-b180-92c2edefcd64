# -*- coding: utf-8 -*-
"""
变形配置定义
"""

from module.MMath import MVector3D, MQuaternion
from .vroid_constants import MORPH_SYSTEM, MORPH_EYEBROW, MORPH_EYE, MORPH_LIP, MORPH_OTHER

# 变形配对定义
MORPH_PAIRS = {
    # 眉毛相关变形
    "Fcl_BRW_Fun_R": {"name": "にこり右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Fun"},
    "Fcl_BRW_Fun_L": {"name": "にこり左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Fun"},
    "Fcl_BRW_Fun": {"name": "にこり", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Joy_R": {"name": "にこり2右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Joy"},
    "Fcl_BRW_Joy_L": {"name": "にこり2左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Joy"},
    "Fcl_BRW_Joy": {"name": "にこり2", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Sorrow_R": {"name": "困る右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Sorrow"},
    "Fcl_BRW_Sorrow_L": {"name": "困る左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Sorrow"},
    "Fcl_BRW_Sorrow": {"name": "困る", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Angry_R": {"name": "怒り右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Angry"},
    "Fcl_BRW_Angry_L": {"name": "怒り左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Angry"},
    "Fcl_BRW_Angry": {"name": "怒り", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Surprised_R": {"name": "驚き右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Surprised"},
    "Fcl_BRW_Surprised_L": {"name": "驚き左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Surprised"},
    "Fcl_BRW_Surprised": {"name": "驚き", "panel": MORPH_EYEBROW},
    
    # 眉毛基础变形
    "brow_Below_R": {"name": "下右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Below_L": {"name": "下左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Below": {"name": "下", "panel": MORPH_EYEBROW, "binds": ["brow_Below_R", "brow_Below_L"]},
    "brow_Abobe_R": {"name": "上右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Abobe_L": {"name": "上左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Abobe": {"name": "上", "panel": MORPH_EYEBROW, "binds": ["brow_Abobe_R", "brow_Abobe_L"]},
    "brow_Left_R": {"name": "右眉左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Left_L": {"name": "左眉左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Left": {"name": "眉左", "panel": MORPH_EYEBROW, "binds": ["brow_Left_R", "brow_Left_L"]},
    "brow_Right_R": {"name": "右眉右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Right_L": {"name": "左眉右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Right": {"name": "眉右", "panel": MORPH_EYEBROW, "binds": ["brow_Right_R", "brow_Right_L"]},
    "brow_Front_R": {"name": "右眉手前", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Front_L": {"name": "左眉手前", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Front": {"name": "眉手前", "panel": MORPH_EYEBROW, "binds": ["brow_Front_R", "brow_Front_L"]},
    
    # 复合眉毛变形
    "brow_Serious_R": {
        "name": "真面目右",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "brow_Below_R"],
        "ratios": [0.25, 0.7],
    },
    "brow_Serious_L": {
        "name": "真面目左",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_L", "brow_Below_L"],
        "ratios": [0.25, 0.7],
    },
    "brow_Serious": {
        "name": "真面目",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "brow_Below_R", "Fcl_BRW_Angry_L", "brow_Below_L"],
        "ratios": [0.25, 0.7, 0.25, 0.7],
    },
    "brow_Frown_R": {
        "name": "ひそめ右",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "Fcl_BRW_Sorrow_R", "brow_Right_R"],
        "ratios": [0.5, 0.5, 0.3],
    },
    "brow_Frown_L": {
        "name": "ひそめ左",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_L", "Fcl_BRW_Sorrow_L", "brow_Left_L"],
        "ratios": [0.5, 0.5, 0.3],
    },
    "brow_Frown": {
        "name": "ひそめ",
        "panel": MORPH_EYEBROW,
        "binds": [
            "Fcl_BRW_Angry_R",
            "Fcl_BRW_Sorrow_R",
            "brow_Right_R",
            "Fcl_BRW_Angry_L",
            "Fcl_BRW_Sorrow_L",
            "brow_Left_L",
        ],
        "ratios": [0.5, 0.5, 0.3, 0.5, 0.5, 0.3],
    },
    
    # 其他眉毛变形
    "browInnerUp_R": {"name": "ひそめる2右", "panel": MORPH_EYEBROW, "split": "browInnerUp"},
    "browInnerUp_L": {"name": "ひそめる2左", "panel": MORPH_EYEBROW, "split": "browInnerUp"},
    "browInnerUp": {"name": "ひそめる2", "panel": MORPH_EYEBROW},
    "browDownRight": {"name": "真面目2右", "panel": MORPH_EYEBROW},
    "browDownLeft": {"name": "真面目2左", "panel": MORPH_EYEBROW},
    "browDown": {"name": "真面目2", "panel": MORPH_EYEBROW, "binds": ["browDownRight", "browDownLeft"]},
    "browOuterUpRight": {"name": "はんっ右", "panel": MORPH_EYEBROW},
    "browOuterUpLeft": {"name": "はんっ左", "panel": MORPH_EYEBROW},
    "browOuter": {"name": "はんっ", "panel": MORPH_EYEBROW, "binds": ["browOuterUpRight", "browOuterUpLeft"]},
    
    # 眼部基础变形
    "Fcl_EYE_Surprised_R": {"name": "びっくり右", "panel": MORPH_EYE, "split": "Fcl_EYE_Surprised"},
    "Fcl_EYE_Surprised_L": {"name": "びっくり左", "panel": MORPH_EYE, "split": "Fcl_EYE_Surprised"},
    "Fcl_EYE_Surprised": {"name": "びっくり", "panel": MORPH_EYE},
    "eye_Small_R": {"name": "瞳小右", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Small_L": {"name": "瞳小左", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Small": {"name": "瞳小", "panel": MORPH_EYE, "binds": ["eye_Small_R", "eye_Small_L"]},
    "eye_Big_R": {"name": "瞳大右", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Big_L": {"name": "瞳大左", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Big": {"name": "瞳大", "panel": MORPH_EYE, "binds": ["eye_Big_R", "eye_Big_L"]},
    
    # 眼部闭合变形
    "Fcl_EYE_Close_R": {"name": "ｳｨﾝｸ２右", "panel": MORPH_EYE},
    "Fcl_EYE_Close_R_Bone": {
        "name": "ｳｨﾝｸ２右ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "右目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, -0.015),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-12, 0, 0),
        ],
    },
    "Fcl_EYE_Close_R_Group": {
        "name": "ｳｨﾝｸ２右連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Close_R",
            "eye_Small_R",
            "Fcl_EYE_Close_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Sorrow_R",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2],
    },
    "Fcl_EYE_Close_L": {"name": "ウィンク２", "panel": MORPH_EYE},
    "Fcl_EYE_Close_L_Bone": {
        "name": "ウィンク２ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "左目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, -0.015),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-12, 0, 0),
        ],
    },
    "Fcl_EYE_Close_L_Group": {
        "name": "ウィンク２連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_L",
            "Fcl_EYE_Close_L",
            "eye_Small_L",
            "Fcl_EYE_Close_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Sorrow_L",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2],
    },
    "Fcl_EYE_Close": {"name": "まばたき", "panel": MORPH_EYE},
    "Fcl_EYE_Close_Group": {
        "name": "まばたき連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Close_R",
            "eye_Small_R",
            "Fcl_EYE_Close_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Sorrow_R",
            "brow_Below_L",
            "Fcl_EYE_Close_L",
            "eye_Small_L",
            "Fcl_EYE_Close_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Sorrow_L",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2, 0.2, 1, 0.3, 1, 0.1, 0.2],
    },

    # 眼部笑容变形
    "Fcl_EYE_Joy_R": {"name": "笑い右", "panel": MORPH_EYE, "split": "Fcl_EYE_Joy"},
    "Fcl_EYE_Joy_L": {"name": "笑い左", "panel": MORPH_EYE, "split": "Fcl_EYE_Joy"},
    "Fcl_EYE_Joy": {"name": "笑い", "panel": MORPH_EYE},
    "Fcl_EYE_Fun_R": {"name": "にこり右", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun"},
    "Fcl_EYE_Fun_L": {"name": "にこり左", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun"},
    "Fcl_EYE_Fun": {"name": "にこり", "panel": MORPH_EYE},

    # 眼部其他表情
    "Fcl_EYE_Sorrow_R": {"name": "悲しみ右", "panel": MORPH_EYE, "split": "Fcl_EYE_Sorrow"},
    "Fcl_EYE_Sorrow_L": {"name": "悲しみ左", "panel": MORPH_EYE, "split": "Fcl_EYE_Sorrow"},
    "Fcl_EYE_Sorrow": {"name": "悲しみ", "panel": MORPH_EYE},
    "Fcl_EYE_Angry_R": {"name": "怒り右", "panel": MORPH_EYE, "split": "Fcl_EYE_Angry"},
    "Fcl_EYE_Angry_L": {"name": "怒り左", "panel": MORPH_EYE, "split": "Fcl_EYE_Angry"},
    "Fcl_EYE_Angry": {"name": "怒り", "panel": MORPH_EYE},

    # 眼部位置变形
    "eye_Up_R": {"name": "上右", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Up_L": {"name": "上左", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Up": {"name": "上", "panel": MORPH_EYE, "binds": ["eye_Up_R", "eye_Up_L"]},
    "eye_Down_R": {"name": "下右", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Down_L": {"name": "下左", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Down": {"name": "下", "panel": MORPH_EYE, "binds": ["eye_Down_R", "eye_Down_L"]},
    "eye_Left_R": {"name": "右目左", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Left_L": {"name": "左目左", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Left": {"name": "目左", "panel": MORPH_EYE, "binds": ["eye_Left_R", "eye_Left_L"]},
    "eye_Right_R": {"name": "右目右", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Right_L": {"name": "左目右", "panel": MORPH_EYE, "creates": ["FaceEye", "EyeIris", "EyeHighlight"]},
    "eye_Right": {"name": "目右", "panel": MORPH_EYE, "binds": ["eye_Right_R", "eye_Right_L"]},

    # 眼部复合变形
    "eye_Glare_R": {
        "name": "ジト目右",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Angry_R", "eye_Down_R"],
        "ratios": [0.5, 0.5],
    },
    "eye_Glare_L": {
        "name": "ジト目左",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Angry_L", "eye_Down_L"],
        "ratios": [0.5, 0.5],
    },
    "eye_Glare": {
        "name": "ジト目",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Angry_R", "eye_Down_R", "Fcl_EYE_Angry_L", "eye_Down_L"],
        "ratios": [0.5, 0.5, 0.5, 0.5],
    },
    "eye_Highlight_R": {"name": "ハイライト右", "panel": MORPH_EYE, "creates": ["EyeHighlight"]},
    "eye_Highlight_L": {"name": "ハイライト左", "panel": MORPH_EYE, "creates": ["EyeHighlight"]},
    "eye_Highlight": {"name": "ハイライト", "panel": MORPH_EYE, "binds": ["eye_Highlight_R", "eye_Highlight_L"]},

    # 嘴部基础变形
    "Fcl_MTH_A": {"name": "あ", "panel": MORPH_LIP},
    "Fcl_MTH_I": {"name": "い", "panel": MORPH_LIP},
    "Fcl_MTH_U": {"name": "う", "panel": MORPH_LIP},
    "Fcl_MTH_E": {"name": "え", "panel": MORPH_LIP},
    "Fcl_MTH_O": {"name": "お", "panel": MORPH_LIP},
    "Fcl_MTH_N": {"name": "ん", "panel": MORPH_LIP},

    # 嘴部表情变形
    "Fcl_MTH_Fun": {"name": "にこり", "panel": MORPH_LIP},
    "Fcl_MTH_Joy": {"name": "にこり2", "panel": MORPH_LIP},
    "Fcl_MTH_Sorrow": {"name": "困る", "panel": MORPH_LIP},
    "Fcl_MTH_Angry": {"name": "怒り", "panel": MORPH_LIP},
    "Fcl_MTH_Surprised": {"name": "驚き", "panel": MORPH_LIP},

    # 嘴部位置变形
    "mouth_Up": {"name": "上", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Down": {"name": "下", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Left": {"name": "左", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Right": {"name": "右", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Front": {"name": "手前", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Back": {"name": "奥", "panel": MORPH_LIP, "creates": ["FaceMouth"]},

    # 嘴部大小变形
    "mouth_Big": {"name": "大", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Small": {"name": "小", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Wide": {"name": "横広", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Narrow": {"name": "横狭", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Tall": {"name": "縦広", "panel": MORPH_LIP, "creates": ["FaceMouth"]},
    "mouth_Short": {"name": "縦狭", "panel": MORPH_LIP, "creates": ["FaceMouth"]},

    # 嘴部复合变形
    "mouth_Smile": {
        "name": "笑い",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Fun", "mouth_Wide"],
        "ratios": [1, 0.3],
    },
    "mouth_Pout": {
        "name": "ぷんっ",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Angry", "mouth_Front"],
        "ratios": [1, 0.5],
    },
    "mouth_Frown": {
        "name": "しかめっ面",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Sorrow", "mouth_Down"],
        "ratios": [1, 0.3],
    },

    # 其他面部变形
    "cheek_Puff_R": {"name": "頬膨らみ右", "panel": MORPH_OTHER, "creates": ["FaceCheek"]},
    "cheek_Puff_L": {"name": "頬膨らみ左", "panel": MORPH_OTHER, "creates": ["FaceCheek"]},
    "cheek_Puff": {"name": "頬膨らみ", "panel": MORPH_OTHER, "binds": ["cheek_Puff_R", "cheek_Puff_L"]},
    "cheek_Suck_R": {"name": "頬へこみ右", "panel": MORPH_OTHER, "creates": ["FaceCheek"]},
    "cheek_Suck_L": {"name": "頬へこみ左", "panel": MORPH_OTHER, "creates": ["FaceCheek"]},
    "cheek_Suck": {"name": "頬へこみ", "panel": MORPH_OTHER, "binds": ["cheek_Suck_R", "cheek_Suck_L"]},

    # 鼻部变形
    "nose_Up": {"name": "鼻上", "panel": MORPH_OTHER, "creates": ["FaceNose"]},
    "nose_Down": {"name": "鼻下", "panel": MORPH_OTHER, "creates": ["FaceNose"]},
    "nose_Left": {"name": "鼻左", "panel": MORPH_OTHER, "creates": ["FaceNose"]},
    "nose_Right": {"name": "鼻右", "panel": MORPH_OTHER, "creates": ["FaceNose"]},
    "nose_Big": {"name": "鼻大", "panel": MORPH_OTHER, "creates": ["FaceNose"]},
    "nose_Small": {"name": "鼻小", "panel": MORPH_OTHER, "creates": ["FaceNose"]},

    # 舌头变形
    "tongue_Out": {"name": "舌出し", "panel": MORPH_OTHER, "creates": ["FaceTongue"]},
    "tongue_Up": {"name": "舌上", "panel": MORPH_OTHER, "creates": ["FaceTongue"]},
    "tongue_Down": {"name": "舌下", "panel": MORPH_OTHER, "creates": ["FaceTongue"]},
    "tongue_Left": {"name": "舌左", "panel": MORPH_OTHER, "creates": ["FaceTongue"]},
    "tongue_Right": {"name": "舌右", "panel": MORPH_OTHER, "creates": ["FaceTongue"]},

    # 系统变形（骨骼变形）
    "eye_Bone_R": {
        "name": "右目ボーン",
        "panel": MORPH_SYSTEM,
        "bone": ["右目"],
        "move_ratios": [MVector3D(0, 0, 0)],
        "rotate_ratios": [MQuaternion()],
    },
    "eye_Bone_L": {
        "name": "左目ボーン",
        "panel": MORPH_SYSTEM,
        "bone": ["左目"],
        "move_ratios": [MVector3D(0, 0, 0)],
        "rotate_ratios": [MQuaternion()],
    },
    "eye_Bone": {
        "name": "両目ボーン",
        "panel": MORPH_SYSTEM,
        "bone": ["両目"],
        "move_ratios": [MVector3D(0, 0, 0)],
        "rotate_ratios": [MQuaternion()],
    },
}
