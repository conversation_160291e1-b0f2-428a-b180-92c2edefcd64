# -*- coding: utf-8 -*-
"""
模型创建和数据读取模块
"""

import os
import json
import struct
import base64
from pathlib import Path
from module.MMath import MVector3D, MVector4D, MQuaternion
from module.PmxModel import PmxModel, DisplaySlot
from mmd_human_dance_pose.module.MLogger import <PERSON>Logger
from .vroid_constants import MIKU_METER

logger = MLogger(__name__)


class ModelCreator:
    """模型创建器"""
    
    def __init__(self, options):
        self.options = options
        self.buffer = None
    
    def create_model(self):
        """创建模型"""
        model = PmxModel()

        # テクスチャ用ディレクトリ
        tex_dir_path = os.path.join(str(Path(self.options.output_path).resolve().parents[0]), "tex")
        os.makedirs(tex_dir_path, exist_ok=True)
        # 展開用ディレクトリ作成
        glft_dir_path = os.path.join(str(Path(self.options.output_path).resolve().parents[0]), "glTF")
        os.makedirs(glft_dir_path, exist_ok=True)
        # PmxTailor設定用ディレクトリ作成
        setting_dir_path = os.path.join(str(Path(self.options.output_path).resolve().parents[0]), "PmxTailorSetting")
        os.makedirs(setting_dir_path, exist_ok=True)

        with open(self.options.vrm_model.path, "rb") as f:
            self.buffer = f.read()

        # GLBファイルの解析
        if len(self.buffer) < 12:
            raise ValueError("Invalid GLB file")

        # GLBヘッダーの読み取り
        magic = struct.unpack("<I", self.buffer[0:4])[0]
        version = struct.unpack("<I", self.buffer[4:8])[0]
        length = struct.unpack("<I", self.buffer[8:12])[0]

        if magic != 0x46546C67:  # "glTF"
            raise ValueError("Not a valid GLB file")

        logger.info(f"GLB version: {version}, length: {length}")

        # チャンクの読み取り
        offset = 12
        json_data = None
        buffer_data = {}

        while offset < len(self.buffer):
            if offset + 8 > len(self.buffer):
                break

            chunk_length = struct.unpack("<I", self.buffer[offset:offset+4])[0]
            chunk_type = struct.unpack("<I", self.buffer[offset+4:offset+8])[0]
            chunk_data = self.buffer[offset+8:offset+8+chunk_length]

            if chunk_type == 0x4E4F534A:  # "JSON"
                json_data = json.loads(chunk_data.decode('utf-8'))
            elif chunk_type == 0x004E4942:  # "BIN\0"
                buffer_data[0] = chunk_data

            offset += 8 + chunk_length
            # パディングを考慮
            if offset % 4 != 0:
                offset += 4 - (offset % 4)

        if json_data is None:
            raise ValueError("No JSON chunk found in GLB file")

        model.json_data = json_data
        model.buffer_data = buffer_data

        # 基本情報の設定
        model.name = self.options.vrm_model.name
        model.english_name = self.options.vrm_model.name
        model.comment = f"Converted from VRoid: {self.options.vrm_model.name}"
        model.english_comment = f"Converted from VRoid: {self.options.vrm_model.name}"

        # 表示枠の初期化
        self._initialize_display_slots(model)

        return model, tex_dir_path, glft_dir_path, setting_dir_path

    def _initialize_display_slots(self, model):
        """表示枠を初期化"""
        # Root表示枠
        root_slot = DisplaySlot(
            name="Root",
            english_name="Root",
            special_flag=1,
            references=[]
        )
        model.display_slots["Root"] = root_slot

        # 表情表示枠
        expression_slot = DisplaySlot(
            name="表情",
            english_name="Exp",
            special_flag=0,
            references=[]
        )
        model.display_slots["表情"] = expression_slot

        # ボーン表示枠
        bone_slot = DisplaySlot(
            name="ボーン",
            english_name="Bone",
            special_flag=0,
            references=[]
        )
        model.display_slots["ボーン"] = bone_slot

    def read_from_accessor(self, model, accessor_idx):
        """アクセサーからデータを読み取り"""
        if "accessors" not in model.json_data or accessor_idx >= len(model.json_data["accessors"]):
            return []

        accessor = model.json_data["accessors"][accessor_idx]
        buffer_view_idx = accessor["bufferView"]
        
        if "bufferViews" not in model.json_data or buffer_view_idx >= len(model.json_data["bufferViews"]):
            return []

        buffer_view = model.json_data["bufferViews"][buffer_view_idx]
        buffer_idx = buffer_view["buffer"]
        
        if buffer_idx not in model.buffer_data:
            return []

        buffer_data = model.buffer_data[buffer_idx]
        
        # オフセットとストライドの計算
        byte_offset = accessor.get("byteOffset", 0) + buffer_view.get("byteOffset", 0)
        byte_stride = buffer_view.get("byteStride", 0)
        count = accessor["count"]
        
        # データタイプとコンポーネントタイプの取得
        component_type = accessor["componentType"]
        accessor_type = accessor["type"]
        
        # コンポーネントサイズの計算
        component_size_map = {
            5120: 1,  # BYTE
            5121: 1,  # UNSIGNED_BYTE
            5122: 2,  # SHORT
            5123: 2,  # UNSIGNED_SHORT
            5125: 4,  # UNSIGNED_INT
            5126: 4,  # FLOAT
        }
        
        type_component_count_map = {
            "SCALAR": 1,
            "VEC2": 2,
            "VEC3": 3,
            "VEC4": 4,
            "MAT2": 4,
            "MAT3": 9,
            "MAT4": 16,
        }
        
        component_size = component_size_map.get(component_type, 4)
        component_count = type_component_count_map.get(accessor_type, 1)
        element_size = component_size * component_count
        
        # ストライドが指定されていない場合は要素サイズを使用
        if byte_stride == 0:
            byte_stride = element_size
        
        # データの読み取り
        data = []
        for i in range(count):
            element_offset = byte_offset + i * byte_stride
            element_data = buffer_data[element_offset:element_offset + element_size]
            data.append(element_data)
        
        return data

    def get_deform_index(self, vidx, model, position, joint, skin_joints, weights, bone_name_dict):
        """変形インデックスを取得"""
        deform_info = {
            'bone_names': [],
            'weights': []
        }
        
        # ジョイントとウェイトの処理
        joint_indices = [int(joint.x), int(joint.y), int(joint.z), int(joint.w)]
        weight_values = [weights[0].x, weights[0].y, weights[0].z, weights[0].w]
        
        # 有効なウェイトのみを抽出
        valid_weights = []
        valid_bone_names = []
        
        for i, (joint_idx, weight) in enumerate(zip(joint_indices, weight_values)):
            if weight > 0.001 and joint_idx < len(skin_joints):
                skin_joint_idx = skin_joints[joint_idx]
                if skin_joint_idx < len(model.json_data.get("nodes", [])):
                    node = model.json_data["nodes"][skin_joint_idx]
                    bone_name = node.get("name", f"bone_{skin_joint_idx}")
                    
                    # ボーン名の変換
                    if bone_name in bone_name_dict:
                        pmx_bone_name = bone_name_dict[bone_name]
                        valid_bone_names.append(pmx_bone_name)
                        valid_weights.append(weight)
        
        # ウェイトの正規化
        if valid_weights:
            total_weight = sum(valid_weights)
            if total_weight > 0:
                valid_weights = [w / total_weight for w in valid_weights]
        
        deform_info['bone_names'] = valid_bone_names
        deform_info['weights'] = valid_weights
        
        return deform_info

    def save_texture_from_uri(self, uri, tex_dir_path, texture_name):
        """URIからテクスチャを保存"""
        try:
            if uri.startswith("data:"):
                # データURIの場合
                header, data = uri.split(",", 1)
                mime_type = header.split(";")[0].split(":")[1]
                
                # MIMEタイプから拡張子を決定
                mime_to_ext = {
                    "image/png": "png",
                    "image/jpeg": "jpg",
                    "image/jpg": "jpg",
                    "image/bmp": "bmp",
                    "image/tga": "tga"
                }
                
                ext = mime_to_ext.get(mime_type, "png")
                if not texture_name.endswith(f".{ext}"):
                    texture_name = f"{texture_name}.{ext}"
                
                texture_path = os.path.join(tex_dir_path, texture_name)
                
                with open(texture_path, "wb") as f:
                    f.write(base64.b64decode(data))
                
                return texture_name
            else:
                # 外部ファイルの場合（通常はGLBでは使用されない）
                logger.warning(f"External texture URI not supported: {uri}")
                return None
        except Exception as e:
            logger.warning(f"テクスチャ保存エラー: {e}")
            return None
