# -*- coding: utf-8 -*-
"""
姿态转换模块
"""

import numpy as np
from module.MMath import MVector3D, MVector4D, MQuaternion, MMatrix4x4
from module.PmxModel import PmxModel, Bdef1, Bdef2, Bdef4
from mmd_human_dance_pose.module.MLogger import MLogger

logger = MLogger(__name__)


class StanceProcessor:
    """姿态处理器"""
    
    def __init__(self):
        pass
    
    def transfer_stance(self, model: PmxModel):
        """转换姿态（A-pose到T-pose）"""
        # 各頂点の相対位置を計算
        all_vertex_relative_poses = {}
        for vertex in model.vertex_dict.values():
            if type(vertex.deform) is Bdef1:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index0]].position
                ]
            elif type(vertex.deform) is Bdef2:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index0]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index1]].position,
                ]
            elif type(vertex.deform) is Bdef4:
                all_vertex_relative_poses[vertex.index] = [
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index0]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index1]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index2]].position,
                    vertex.position - model.bones[model.bone_indexes[vertex.deform.index3]].position,
                ]

        # A-poseからT-poseへの変換行列を定義
        stance_matrices = self._get_stance_matrices()

        # 各ボーンに変換行列を適用
        for bone_name, matrix in stance_matrices.items():
            if bone_name in model.bones:
                bone = model.bones[bone_name]
                
                # ボーンの位置と回転を変換
                bone_matrix = MMatrix4x4()
                bone_matrix.setToIdentity()
                bone_matrix.translate(bone.position)
                
                # 変換行列を適用
                transformed_matrix = matrix * bone_matrix
                
                # 新しい位置を設定
                bone.position = MVector3D(
                    transformed_matrix.data()[0, 3],
                    transformed_matrix.data()[1, 3],
                    transformed_matrix.data()[2, 3]
                )

        # 頂点位置を再計算
        for vertex in model.vertex_dict.values():
            if type(vertex.deform) is Bdef1:
                bone_idx = vertex.deform.index0
                bone_name = model.bone_indexes[bone_idx]
                if bone_name in stance_matrices:
                    relative_pos = all_vertex_relative_poses[vertex.index][0]
                    transformed_pos = stance_matrices[bone_name] * MVector4D(relative_pos.x, relative_pos.y, relative_pos.z, 1)
                    vertex.position = model.bones[bone_name].position + MVector3D(transformed_pos.x, transformed_pos.y, transformed_pos.z)
                    
                    # 法線も変換
                    vertex.normal = self.calc_normal(stance_matrices[bone_name], vertex.normal)
            
            elif type(vertex.deform) is Bdef2:
                bone_idx0 = vertex.deform.index0
                bone_idx1 = vertex.deform.index1
                bone_name0 = model.bone_indexes[bone_idx0]
                bone_name1 = model.bone_indexes[bone_idx1]
                weight0 = vertex.deform.weight0
                weight1 = 1.0 - weight0
                
                new_pos = MVector3D()
                new_normal = MVector3D()
                
                if bone_name0 in stance_matrices:
                    relative_pos0 = all_vertex_relative_poses[vertex.index][0]
                    transformed_pos0 = stance_matrices[bone_name0] * MVector4D(relative_pos0.x, relative_pos0.y, relative_pos0.z, 1)
                    pos0 = model.bones[bone_name0].position + MVector3D(transformed_pos0.x, transformed_pos0.y, transformed_pos0.z)
                    normal0 = self.calc_normal(stance_matrices[bone_name0], vertex.normal)
                    new_pos += pos0 * weight0
                    new_normal += normal0 * weight0
                
                if bone_name1 in stance_matrices:
                    relative_pos1 = all_vertex_relative_poses[vertex.index][1]
                    transformed_pos1 = stance_matrices[bone_name1] * MVector4D(relative_pos1.x, relative_pos1.y, relative_pos1.z, 1)
                    pos1 = model.bones[bone_name1].position + MVector3D(transformed_pos1.x, transformed_pos1.y, transformed_pos1.z)
                    normal1 = self.calc_normal(stance_matrices[bone_name1], vertex.normal)
                    new_pos += pos1 * weight1
                    new_normal += normal1 * weight1
                
                vertex.position = new_pos
                vertex.normal = new_normal.normalized()
            
            elif type(vertex.deform) is Bdef4:
                bone_indices = [vertex.deform.index0, vertex.deform.index1, vertex.deform.index2, vertex.deform.index3]
                weights = [vertex.deform.weight0, vertex.deform.weight1, vertex.deform.weight2, vertex.deform.weight3]
                
                new_pos = MVector3D()
                new_normal = MVector3D()
                
                for i, (bone_idx, weight) in enumerate(zip(bone_indices, weights)):
                    if weight > 0 and bone_idx < len(model.bone_indexes):
                        bone_name = model.bone_indexes[bone_idx]
                        if bone_name in stance_matrices:
                            relative_pos = all_vertex_relative_poses[vertex.index][i]
                            transformed_pos = stance_matrices[bone_name] * MVector4D(relative_pos.x, relative_pos.y, relative_pos.z, 1)
                            pos = model.bones[bone_name].position + MVector3D(transformed_pos.x, transformed_pos.y, transformed_pos.z)
                            normal = self.calc_normal(stance_matrices[bone_name], vertex.normal)
                            new_pos += pos * weight
                            new_normal += normal * weight
                
                vertex.position = new_pos
                vertex.normal = new_normal.normalized()

        return model

    def calc_normal(self, bone_mat: MMatrix4x4, normal: MVector3D):
        """法線を計算"""
        # ボーン行列の3x3行列
        bone_invert_mat = bone_mat.data()[:3, :3]
        
        return MVector3D(np.sum(normal.data() * bone_invert_mat, axis=1)).normalized()

    def _get_stance_matrices(self):
        """A-poseからT-poseへの変換行列を取得"""
        matrices = {}
        
        # 左腕の変換（A-poseからT-poseへ）
        left_arm_rotation = MQuaternion.fromEulerAngles(0, 0, -45)  # Z軸周りに-45度回転
        left_arm_matrix = MMatrix4x4()
        left_arm_matrix.setToIdentity()
        left_arm_matrix.rotate(left_arm_rotation)
        matrices["左腕"] = left_arm_matrix
        
        # 右腕の変換
        right_arm_rotation = MQuaternion.fromEulerAngles(0, 0, 45)  # Z軸周りに45度回転
        right_arm_matrix = MMatrix4x4()
        right_arm_matrix.setToIdentity()
        right_arm_matrix.rotate(right_arm_rotation)
        matrices["右腕"] = right_arm_matrix
        
        # 左肩の変換
        left_shoulder_rotation = MQuaternion.fromEulerAngles(0, 0, -30)
        left_shoulder_matrix = MMatrix4x4()
        left_shoulder_matrix.setToIdentity()
        left_shoulder_matrix.rotate(left_shoulder_rotation)
        matrices["左肩"] = left_shoulder_matrix
        
        # 右肩の変換
        right_shoulder_rotation = MQuaternion.fromEulerAngles(0, 0, 30)
        right_shoulder_matrix = MMatrix4x4()
        right_shoulder_matrix.setToIdentity()
        right_shoulder_matrix.rotate(right_shoulder_rotation)
        matrices["右肩"] = right_shoulder_matrix
        
        # 左ひじの変換
        left_elbow_rotation = MQuaternion.fromEulerAngles(0, 0, -15)
        left_elbow_matrix = MMatrix4x4()
        left_elbow_matrix.setToIdentity()
        left_elbow_matrix.rotate(left_elbow_rotation)
        matrices["左ひじ"] = left_elbow_matrix
        
        # 右ひじの変換
        right_elbow_rotation = MQuaternion.fromEulerAngles(0, 0, 15)
        right_elbow_matrix = MMatrix4x4()
        right_elbow_matrix.setToIdentity()
        right_elbow_matrix.rotate(right_elbow_rotation)
        matrices["右ひじ"] = right_elbow_matrix
        
        return matrices
