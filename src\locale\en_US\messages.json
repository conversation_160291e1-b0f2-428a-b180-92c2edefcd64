{"（ログあり版）": "(Version with log)", "PmxTailor ローカル版": "PmxTailor local version", "PmxTailor実行": "Run PmxTailor", "PmxTailor停止": "Stop PmxTailor", "PMXモデルの指定された材質に物理を設定します。\n": "Sets the physics to the specified material for the PMX model.\n", "PMXモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "After loading the PMX model, set the physics on the Para Adjustment tab.", "対象モデル": "Object model", "対象モデルPMXファイルを開く": "Open the target model PMX file", "変換したいPMXファイルパスを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "Specify the PMX file path you want to convert.\nYou can specify by D & D, specify from the open button, and select from the history.", "出力対象PMX": "Output target PMX", "出力対象PMXファイルを開く": "Open the output target PMX file", "変換結果PMX出力パスを指定してください。\n対象モデルPMXファイル名に基づいて自動生成されますが、任意のパスに変更することも可能です。": "Specify the conversion result PMX output path.\nIt is automatically generated based on the target model PMX file name, but you can change it to any path.", "PMXモデルに物理を設定します": "Set physics for PMX model", "ファイル": "File", "材質を選択して、パラメーターを調整してください。": "Select the material and adjust the parameters.", "材質設定クリア": "Material setting clear", "既に入力されたデータをすべて空にします。": "Empty all the data already entered.", "物理設定追加": "Add physical settings", "物理設定フォームをパネルに追加します。": "Add the physics form to the panel.", "パラ調整": "Para adjustment", "パラ調整タブで材質を選択して、パラメーターを調整してください。\n": "Select the material on the Para adjustment tab and adjust the parameters.\n", "※パラ調整タブで変更した値は詳細タブに反映されますが、逆方向には反映されません": "* The value changed on the Para adjustment tab is reflected on the Details tab, but not in the opposite direction.", "パラ調整(詳細)": "Para adjustment (details)", "パラ調整タブで選択された材質に既にボーンとウェイトが設定されている場合に、\n": "If bones and weights have already been set for the material selected on the Para Adjustment tab,\n", "ボーン構成を指定する事で物理設定（剛体・ジョイント）を設定する事が出来ます。\n": "Physical settings (rigid body / joint) can be set by specifying the bone configuration.\n", "縦方向がボーンの親子関係、横がボーンの並び順で指定してください。\n": "Specify the parent-child relationship of bones in the vertical direction and the order of bones in the horizontal direction.\n", "（スカートなどは水平方向の並び順、袖などは輪切り縦方向の並び順を横方向に並べてください）\n": "(For skirts, etc., arrange them in the horizontal direction, and for sleeves, etc., arrange them in the vertical direction.)\n", "ボーン名を指定すると、その子ボーンを自動設定します。（少しタイムラグがあります）\n": "If you specify a bone name, its child bones will be set automatically. (There is a little time lag)\n", "水平方向のボーン間が繋がっているかは、ウェイトを見て自動判定します。": "Whether or not the horizontal bones are connected is automatically determined by looking at the weight.", "パラ調整(ボーン)": "Para adjustment (bone)", "パラ調整タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "Start preparation for para adjustment tab display\nExecutes the file reading process. please wait a moment....", "読み込み処理停止": "Stop reading process", "-- PMX 頂点読み込み完了": "--PMX vertex read completed", "-- PMX 面読み込み完了": "--PMX surface read completed", "-- PMX テクスチャ読み込み完了": "--PMX texture loading completed", "-- PMX 材質読み込み完了": "--PMX material loading completed", "-- PMX ボーン読み込み完了": "--PMX bone loading completed", "-- PMX モーフ読み込み完了": "--PMX morph loading completed", "-- PMX 表示枠読み込み完了": "--PMX display frame reading completed", "-- PMX 剛体読み込み完了": "--PMX Rigid body reading completed", "-- PMX ジョイント読み込み完了": "--PMX joint loading completed", "%s%s 読み込み成功: %s": " %s %s read successfully: %s", "ファイルデータ読み込みが完了しました": "File data reading is complete", "インポート ...": "Import ...", "材質設定データをjsonファイルから読み込みます。\nファイル選択ダイアログが開きます。": "Read the material setting data from the json file.\nA file selection dialog opens.", "エクスポート ...": "Export ...", "材質設定データをjsonファイルに出力します。\n出力先を指定できます。": "Output the material setting data to the json file.\nYou can specify the output destination.", "物理材質 *": "Physical material *", "物理を設定する材質を選択してください。\n材質全体に物理を設定するため、裾など一部にのみ物理を設定したい場合、材質を一旦分離してください。": "Select the material for which you want to set the physics.\nSince the physics is set for the entire material, if you want to set the physics only for a part such as the hem, separate the materials once.", "親ボーン *": "Parent bone *", "材質物理の起点となる親ボーン\nボーン追従剛体を持っているボーンのみが対象となります。\n（指定された親ボーンの子に「○○中心」ボーンを追加して、それを起点に物理を設定します）": "Parent bone that is the starting point of material physics\nOnly bones that have a bone-following rigid body are eligible.\n(Add a \"○○ center\" bone to the child of the specified parent bone and set the physics from that as the starting point)", "材質略称 *": "Material abbreviation *", "ボーン名などに使用する材質略称を半角6文字 or 全角3文字以内で入力してください。（任意変更可能。その場合は3文字まで）": "Enter the material abbreviation used for the bone name, etc. within 6 half-width characters or 3 full-width characters. (Can be changed arbitrarily. In that case, up to 3 characters)", "剛体グループ *": "Rigid body group *", "剛体のグループ。初期設定では、自分自身のグループのみ非衝突として設定します。": "A group of rigid bodies. By default, only your own group is set as non-collision.", "物理方向": "Physical direction", "物理材質の向き(例：左腕側の物理を設定したい場合に「左」を設定して、物理が流れる方向を左方向に伸ばす)": "Orientation of physical material (Example: If you want to set the physics on the left arm side, set \"Left\" and extend the direction in which the physics flows to the left)", "下": "under", "上": "Up", "右": "right", "左": "left", "既存設定": "Existing settings", "指定された材質に割り当てられている既存物理（ボーン・剛体・ジョイント）がある場合の挙動\nそのまま：処理しない\n": "Behavior when there are existing physics (bones, rigid bodies, joints) assigned to the specified material\nAs it is: Do not process\n", "再利用：ボーンとウェイトは既存のものを利用し、剛体とジョイントだけ作り直す\n上書き：ボーン・剛体・ジョイントを削除して作り直す": "Reuse: Use existing bones and weights and recreate only rigid bodies and joints\nOverwrite: Delete bones, rigid bodies, and joints and recreate them.", "そのまま": "As it is", "再利用": "Reuse", "上書き": "Overwrite", "プリセット": "preset", "物理の参考値プリセット": "Physics reference value preset", "布(コットン)": "Cloth (cotton)", "布(シルク)": "<PERSON><PERSON><PERSON> (silk)", "布(ベルベッド)": "Clot<PERSON> (belbed)", "布(レザー)": "Cloth (leather)", "布(デニム)": "Cloth (denim)", "裏面材質": "Back side material", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "If there is a material that corresponds to the back side of the physical material, select it.\nAssign weights according to the bone split of the physical material", "検出度": "Detectability", "材質内の頂点を検出する時の傾き等の類似度\n値を小さくすると傾きが違っていても検出しやすくなりますが、誤検知が増える可能性があります。": "Similarity such as inclination when detecting vertices in the material\nDecreasing the value makes it easier to detect even if the slope is different, but it may increase false positives.", "（0.75）": "(0.75)", "0.5": "0.5", "1": "1", "細かさ": "Fineness", "材質の物理の細かさ。ボーン・剛体・ジョイントの細かさ等に影響します。": "The physical fineness of the material. It affects the fineness of bones, rigid bodies, joints, etc.", "（3.4）": "(3.4)", "小": "small", "大": "Big", "質量": "mass", "材質の質量。剛体の質量・減衰等に影響します。": "Material mass. It affects the mass and damping of rigid bodies.", "（0.5）": "(0.5)", "軽": "Light", "重": "Heavy", "柔らかさ": "soft", "材質の柔らかさ。大きくなるほどすぐに元の形状に戻ります。（減衰が高い）\n剛体の減衰・ジョイントの強さ等に影響します。": "The softness of the material. The larger it is, the sooner it returns to its original shape. (High attenuation)\nIt affects the damping of rigid bodies and the strength of joints.", "（1.8）": "(1.8)", "柔": "Soft", "硬": "Hard", "張り": "Tension", "材質の形状維持強度。ジョイントの強さ等に影響します。": "Shape maintenance strength of the material. It affects the strength of the joint.", "（1.5）": "(1.5)", "弱": "weak", "強": "strength", "（材質未選択）": "(Material not selected)", "ボーン密度": "Bone density", "縦密度": "Vertical density", "ボーンの縦方向のメッシュに対する密度": "Density of bones relative to the vertical mesh", "横密度": "Lateral density", "ボーンの横方向のメッシュに対する密度": "Density of bones relative to the lateral mesh", "物理タイプ": "Physical type", "布": "cloth", "末端剛体": "End rigid body", "末端剛体の質量": "Mass of the terminal rigid body", "移動減衰": "Movement attenuation", "末端剛体の移動減衰": "Movement damping of the terminal rigid body", "回転減衰": "Rotational damping", "末端剛体の回転減衰": "Rotational damping of the terminal rigid body", "反発力": "Repulsive force", "末端剛体の反発力": "Repulsive force of the terminal rigid body", "摩擦力": "Friction force", "末端剛体の摩擦力": "Friction force of the terminal rigid body", "係数": "coefficient", "末端剛体から上の剛体にかけての加算係数": "Addition coefficient from the terminal rigid body to the upper rigid body", "剛体形状": "Rigid body shape", "剛体の形状": "Rigid body shape", "球": "ball", "箱": "box", "カプセル": "capsule", "縦ジョイント": "Vertical joint", "有効": "valid", "縦ジョイントを有効にするか否か": "Whether to enable vertical joints", "制限係数": "Limit factor", "根元ジョイントが末端ジョイントよりどれくらい制限を強くするか。1の場合、全ての段の制限が均一になります。": "How much the root joint is more restrictive than the end joint. If it is 1, the restrictions on all stages will be uniform.", "移動X(最小)": "Move X (minimum)", "末端縦ジョイントの移動X(最小)": "Movement of end vertical joint X (minimum)", "移動Y(最小)": "Move Y (minimum)", "末端縦ジョイントの移動Y(最小)": "Movement of end vertical joint Y (minimum)", "移動Z(最小)": "Move Z (minimum)", "末端縦ジョイントの移動Z(最小)": "Movement of end vertical joint Z (minimum)", "移動X(最大)": "Move X (maximum)", "末端縦ジョイントの移動X(最大)": "Movement of end vertical joint X (maximum)", "移動Y(最大)": "Move Y (maximum)", "末端縦ジョイントの移動Y(最大)": "Movement of end vertical joint Y (maximum)", "移動Z(最大)": "Move Z (maximum)", "末端縦ジョイントの移動Z(最大)": "Movement of end vertical joint Z (maximum)", "回転X(最小)": "Rotation X (minimum)", "末端縦ジョイントの回転X(最小)": "Rotation of end longitudinal joint X (minimum)", "回転Y(最小)": "Rotation Y (minimum)", "末端縦ジョイントの回転Y(最小)": "Rotation of end longitudinal joint Y (minimum)", "回転Z(最小)": "Rotation Z (minimum)", "末端縦ジョイントの回転Z(最小)": "Rotation Z of end longitudinal joint Z (minimum)", "回転X(最大)": "Rotation X (maximum)", "末端縦ジョイントの回転X(最大)": "Rotation of end vertical joint X (maximum)", "回転Y(最大)": "Rotation Y (maximum)", "末端縦ジョイントの回転Y(最大)": "Rotation of end vertical joint Y (maximum)", "回転Z(最大)": "Rotation Z (maximum)", "末端縦ジョイントの回転Z(最大)": "Rotation Z of end vertical joint (maximum)", "ばね(移動X)": "Spring (movement X)", "末端縦ジョイントのばね(移動X)": "End vertical joint spring (movement X)", "ばね(移動Y)": "Spring (movement Y)", "末端縦ジョイントのばね(移動Y)": "Spring of end vertical joint (movement Y)", "ばね(移動Z)": "Spring (movement Z)", "末端縦ジョイントのばね(移動Z)": "End vertical joint spring (movement Z)", "ばね(回転X)": "Spring (rotation X)", "末端縦ジョイントのばね(回転X)": "End vertical joint spring (rotation X)", "ばね(回転Y)": "Spring (rotation Y)", "末端縦ジョイントのばね(回転Y)": "Spring of end vertical joint (rotation Y)", "ばね(回転Z)": "Spring (rotation Z)", "末端縦ジョイントのばね(回転Z)": "Spring of end vertical joint (rotation Z)", "横ジョイント": "Horizontal joint", "横ジョイントを有効にするか否か": "Whether to enable horizontal joints", "末端横ジョイントの移動X(最小)": "Movement of end lateral joint X (minimum)", "末端横ジョイントの移動Y(最小)": "Movement of end lateral joint Y (minimum)", "末端横ジョイントの移動Z(最小)": "Movement of end lateral joint Z (minimum)", "末端横ジョイントの移動X(最大)": "Movement of end lateral joint X (maximum)", "末端横ジョイントの移動Y(最大)": "Movement of end lateral joint Y (maximum)", "末端横ジョイントの移動Z(最大)": "Movement of end lateral joint Z (maximum)", "末端横ジョイントの回転X(最小)": "Rotation of end lateral joint X (minimum)", "末端横ジョイントの回転Y(最小)": "Rotation of end lateral joint Y (minimum)", "末端横ジョイントの回転Z(最小)": "Rotation Z of end lateral joint Z (minimum)", "末端横ジョイントの回転X(最大)": "Rotation of end lateral joint X (maximum)", "末端横ジョイントの回転Y(最大)": "Rotation of end lateral joint Y (maximum)", "末端横ジョイントの回転Z(最大)": "Rotation Z of end lateral joint (maximum)", "末端横ジョイントのばね(移動X)": "End lateral joint spring (movement X)", "末端横ジョイントのばね(移動Y)": "Spring of end lateral joint (movement Y)", "末端横ジョイントのばね(移動Z)": "Spring of end lateral joint (movement Z)", "末端横ジョイントのばね(回転X)": "Spring of end lateral joint (rotation X)", "末端横ジョイントのばね(回転Y)": "Spring of end lateral joint (rotation Y)", "末端横ジョイントのばね(回転Z)": "Spring of end lateral joint (rotation Z)", "斜めジョイント": "Diagonal joint", "斜めジョイントを有効にするか否か": "Whether to enable diagonal joints", "末端斜めジョイントの移動X(最小)": "Movement of end diagonal joint X (minimum)", "末端斜めジョイントの移動Y(最小)": "Movement of end diagonal joint Y (minimum)", "末端斜めジョイントの移動Z(最小)": "Movement of end diagonal joint Z (minimum)", "末端斜めジョイントの移動X(最大)": "Movement of end diagonal joint X (maximum)", "末端斜めジョイントの移動Y(最大)": "Movement of end diagonal joint Y (maximum)", "末端斜めジョイントの移動Z(最大)": "Movement of end diagonal joint Z (maximum)", "末端斜めジョイントの回転X(最小)": "Rotation of diagonal end joint X (minimum)", "末端斜めジョイントの回転Y(最小)": "Rotation of diagonal end joint Y (minimum)", "末端斜めジョイントの回転Z(最小)": "Rotation Z of end diagonal joint Z (minimum)", "末端斜めジョイントの回転X(最大)": "Rotation of diagonal end joint X (maximum)", "末端斜めジョイントの回転Y(最大)": "Rotation of diagonal end joint Y (maximum)", "末端斜めジョイントの回転Z(最大)": "Rotation Z of end diagonal joint Z (maximum)", "末端斜めジョイントのばね(移動X)": "Spring of diagonal end joint (movement X)", "末端斜めジョイントのばね(移動Y)": "Spring of diagonal end joint (movement Y)", "末端斜めジョイントのばね(移動Z)": "Spring of diagonal end joint (movement Z)", "末端斜めジョイントのばね(回転X)": "Spring of diagonal end joint (rotation X)", "末端斜めジョイントのばね(回転Y)": "Spring of diagonal end joint (rotation Y)", "末端斜めジョイントのばね(回転Z)": "Spring of diagonal end joint (rotation Z)", "逆ジョイント": "Reverse joint", "逆ジョイントを有効にするか否か": "Whether to enable reverse joints", "末端逆ジョイントの移動X(最小)": "End reverse joint movement X (minimum)", "末端逆ジョイントの移動Y(最小)": "Movement of end reverse joint Y (minimum)", "末端逆ジョイントの移動Z(最小)": "End reverse joint movement Z (minimum)", "末端逆ジョイントの移動X(最大)": "Movement of end reverse joint X (maximum)", "末端逆ジョイントの移動Y(最大)": "Movement of end reverse joint Y (maximum)", "末端逆ジョイントの移動Z(最大)": "Movement of end reverse joint Z (maximum)", "末端逆ジョイントの回転X(最小)": "Rotation of end reverse joint X (minimum)", "末端逆ジョイントの回転Y(最小)": "Rotation of end reverse joint Y (minimum)", "末端逆ジョイントの回転Z(最小)": "Rotation Z of end reverse joint Z (minimum)", "末端逆ジョイントの回転X(最大)": "Rotation of end reverse joint X (maximum)", "末端逆ジョイントの回転Y(最大)": "Rotation of end reverse joint Y (maximum)", "末端逆ジョイントの回転Z(最大)": "Rotation Z of end reverse joint (maximum)", "末端逆ジョイントのばね(移動X)": "End reverse joint spring (movement X)", "末端逆ジョイントのばね(移動Y)": "End reverse joint spring (movement Y)", "末端逆ジョイントのばね(移動Z)": "End reverse joint spring (movement Z)", "末端逆ジョイントのばね(回転X)": "End reverse joint spring (rotation X)", "末端逆ジョイントのばね(回転Y)": "End reverse joint spring (rotation Y)", "末端逆ジョイントのばね(回転Z)": "End reverse joint spring (rotation Z)", "ボーン設定データをcsvファイルから読み込みます。\nファイル選択ダイアログが開きます。": "Read the bone setting data from the csv file.\nA file selection dialog opens.", "ボーン設定データをcsvファイルに出力します。\n出力先を指定できます。": "Output the bone setting data to a csv file.\nYou can specify the output destination.", "パラ調整(詳細)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "Para adjustment (details) tab display preparation started\nExecutes the file reading process. please wait a moment....", "PmxTailor変換処理実行": "PmxTailor conversion process execution", "元モデル": "Former model", "材質": "Material", "剛体グループ": "Rigid body group", "【%s】頂点マップ生成": "[%s] Vertex map generation", "%s: 面の抽出": " %s: Face extraction", "%s: 面の抽出準備①": " %s: Preparation for surface extraction ①", "%s: 面の抽出準備②": " %s: Preparation for surface extraction ②", "%s: 相対頂点マップの生成": " %s: Relative vertex map generation", "-- 面: %s個目:終了": "--Surface: %sth: End", "%s: 絶対頂点マップの生成": " %s: Absolute vertex map generation", "-- 絶対頂点マップ: %s個目: ---------": "--Absolute vertex map: %sth: ---------", "-- 絶対頂点マップ: %s個目:終了 ---------": "--Absolute vertex map: %s th: End ---------", "【%s】ボーン生成": "[%s] Bone generation", "【%s(No.%s)】ウェイト分布": "[%s (No.%s)] Weight distribution", "-- 頂点ウェイト: %s個目:終了": "--Vertex weight: %s th: End", "【%s(No.%s)】剛体生成": "[%s (No.%s)] Rigid body generation", "-- 剛体: %s個目:終了": "--Rigid body: %s th: End", "【%s(No.%s)】ジョイント生成": "[%s (No. %s)] Joint generation", "-- ジョイント: %s個目:終了": "--Joint: %sth: End", "PMX出力開始": "PMX output start", "-- 頂点データ出力終了(%s)": "--End of vertex data output (%s)", "-- 面データ出力終了(%s)": "--End of surface data output (%s)", "-- テクスチャデータ出力終了(%s)": "--End of texture data output (%s)", "-- 材質データ出力終了(%s)": "--End of material data output (%s)", "-- ボーンデータ出力終了(%s)": "--Bone data output end (%s)", "-- モーフデータ出力終了(%s)": "--End of morph data output (%s)", "-- 表示枠データ出力終了(%s)": "--Display frame data output end (%s)", "-- 剛体データ出力終了(%s)": "--Rigid body data output end (%s)", "-- ジョイントデータ出力終了(%s)": "--End of joint data output (%s)", "出力終了: %s": "End of output: %s", "PmxTailor変換処理が意図せぬエラーで終了しました。\n\n%s": "The PmxTailor conversion process ended with an unintended error.\n\n %s", "\n処理時間: {0}": "\nProcessing time: {0}", "\n処理時間: 05s": "\nProcessing time: 05s", "既存物理を再利用する場合、「パラ調整(ボーン)」画面でボーン並び順を指定してください。": "When reusing existing physics, specify the bone arrangement order on the \"Para adjustment (bone)\" screen.", "有効な物理設定が1件も設定されていません。\nモデルを選択しなおした場合、物理設定は初期化されます。": "No valid physical settings have been set.\nIf you reselect the model, the physical settings will be initialized.", "【%s】残ウェイト分布": "[%s] Remaining weight distribution", "-- 残頂点ウェイト: %s個目:終了": "--Remaining vertex weight: %sth: End", "\n処理時間: 04s": "\nProcessing time: 04s", "\n処理時間: 08s": "\nProcessing time: 08s", "ボーン設定CSVを読み込む": "Read bone settings CSV", "ボーン設定CSVのインポートに成功しました \n{0}": "Succeeded in importing bone setting CSV\n{0}", "ボーン設定CSVのインポートに成功しました \n%s": "Succeeded in importing bone setting CSV\n %s", "パラ調整(ボーン)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "Para adjustment (bone) tab display preparation started\nExecutes the file reading process. please wait a moment....", "1番目の対象モデルが見つかりませんでした。\n入力パス: %s": "The first target model was not found.\nInput path: %s", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。": "The \"{0}\" tab cannot be opened because the target model file path is not specified in the \"File\" tab.", "\n既に指定済みの場合、現在読み込み中の可能性があります。": "\nIf it has already been specified, it may be currently loading.", "\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "\nOpen the \"{0}\" tab after the \"■ Read successful\" log appears.", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "The \"{0}\" tab cannot be opened because the target model file path is not specified in the \"File\" tab.\nIf it has already been specified, it may be currently loading.\nOpen the \"{0}\" tab after the \"■ Read successful\" log appears.", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「%s」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「%s」タブを開いてください。": "The \" %s\" tab cannot be opened because the target model file path is not specified in the \"File\" tab.\nIf it has already been specified, it may be currently loading.\nOpen the \" %s\" tab after the \"■ Read successful\" log appears.", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「パラ調整」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「パラ調整」タブを開いてください。": "Since the target model file path is not specified in the \"File\" tab, the \"Para adjustment\" tab cannot be opened.\nIf it has already been specified, it may be currently loading.\nAfter the \"■ Read successful\" log appears, open the \"Para adjustment\" tab.", "\n処理時間: %s": "\nProcessing time: %s", "ボーン設定CSVを保存する": "Save the bone settings CSV", "ボーン設定CSVのエクスポートに成功しました \n{0}": "Successful export of bone settings CSV\n{0}", "「パラ調整(ボーン)」画面でボーン並び順を指定した場合、既存物理は「再利用」を指定してください。": "If you specify the bone order on the \"Para Adjustment (Bone)\" screen, specify \"Reuse\" for the existing physics.", "【%s】既存材質削除": "[%s] Delete existing material", "%s: 削除対象抽出": " %s: Extract to be deleted", "%s: 削除実行": " %s: Delete execution", "%s: INDEX振り直し": " %s: INDEX reassignment", "%s: INDEX再割り当て": " %s: INDEX reassignment", "【%s】ボーンマップ生成": "[%s] Bone map generation", "【%s】剛体生成": "[%s] Rigid body generation", "【%s】ジョイント生成": "[%s] Joint generation", "履歴": "history", "これまで指定された対象モデルを再指定できます。": "You can respecify the previously specified target model.", "開く": "open", "ファイルを選んでダブルクリック、またはOKボタンをクリックしてください。": "Select the file and double-click it, or click the OK button.", "{0}番目の": "{0} th", "【%s】裏面ウェイト分布": "[%s] Backside weight distribution", "-- 裏頂点ウェイト: %s個目:終了": "--Back vertex weight: %sth: End", "成功": "Success", "髪": "hair", "物理": "Physics", "Vroid2Pmx ローカル版": "Vroid2Pmx local version", "Vroid2Pmx実行": "Run Vroid2Pmx", "Vroid2Pmx停止": "Stop Vroid2Pmx", "Vrmモデルの指定された材質に物理を設定します。\n": "Sets the physics to the specified material of the Vrm model.\n", "Vrmモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "After loading the Vrm model, set the physics on the Para-adjustment tab.", "対象モデルVrmファイルを開く": "Open the target model Vrm file", "変換したいVrmファイルパスを指定してください\nVroid Studio 正式版(1.0.0)以降のみ対応しています。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "Specify the Vrm file path you want to convert\nOnly Vroid Studio official version (1.0.0) or later is supported.\nYou can specify by D & D, specify from the open button, and select from the history.", "Vrmモデルに物理を設定します": "Set the physics on the Vrm model", "-- JSON出力終了": "--End JSON output", "-- テクスチャデータ解析終了": "--End of texture data analysis", "Vroid2Pmx実行処理を中断します。": "Vroid2Pmx Execution processing is interrupted.", "-- -- Accessor[%s/%s/%s]": "---- Accessor [ %s / %s / %s]", "-- -- Accessor[%s/%s/%s][%s]": "---- Accessor [ %s / %s / %s] [ %s]", "物理を設定する材質を選択してください。\n裾など一部にのみ物理を設定したい場合、頂点データCSVを指定してください。": "Select the material for which you want to set the physics.\nIf you want to set the physics only for a part such as the hem, specify the vertex data CSV.", "対象頂点CSV": "Target vertex CSV", "対象頂点CSVファイルを開く": "Open the target vertex CSV file", "材質の中で物理を割り当てたい頂点を絞り込みたい場合、PmxEditorで頂点リストを選択できるようにして保存した頂点CSVファイルを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "If you want to narrow down the vertices to which you want to assign physics in the material, specify the saved vertex CSV file so that you can select the vertex list in PmxEditor.\nYou can specify by D & D, specify from the open button, and select from the history.", "これまで指定された対象頂点CSVを再指定できます。": "You can respecify the previously specified target vertex CSV.", "%s: 面の抽出準備③": " %s: Preparation for surface extraction ③", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質の最も近い頂点ウェイトを転写します": "If there is a material that corresponds to the back side of the physical material, select it.\nTransfers the closest vertex weight of the physical material", "裾材質": "Hem material", "物理材質の裾にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "If there is a material that corresponds to the hem of the physical material, select it.\nAssign weights according to the bone split of the physical material", "【%s】裾ウェイト分布": "[%s] Hem weight distribution", "Pmxモデルに物理を設定します": "Set physics for Pmx model", "材質を選択して、パラメーターを調整してください。\nスライダーパラメーターで調整した設定に基づいて詳細タブ内のMMD物理パラメーターを変更します。\n物理を再利用したい場合は、ボーンパネルでボーンの並び順を指定してください。": "Select the material and adjust the parameters.\nChange the MMD physical parameters in the Details tab based on the settings adjusted with the slider parameters.\nIf you want to reuse the physics, specify the order of the bones in the bones panel.", "親ボーン *　": "Parent bone *", "裾材質　　": "Hem material", "裏面材質　": "Back side material", "VrmモデルをPmxモデルに変換します。\n": "Convert the Vrm model to the Pmx model.\n", "物理を変えたい場合は、変換後のPmxデータをPmxTailorにかけてください。": "If you want to change the physics, apply the converted Pmx data to PmxTailor.", "VrmモデルをPmxモデルに変換します": "Convert Vrm model to Pmx model", "-- 頂点データ解析[%s]": "--Vertex data analysis [ %s]", "-- 面・材質データ解析[%s-%s]": "--Surface / material data analysis [ %s- %s]", "-- ボーンデータ解析終了": "--Bone data analysis completed", "-- 頂点・面・材質データ解析終了": "--End of analysis of vertex / surface / material data", "-- ボーンデータ調整終了": "--Bone data adjustment completed", "設定クリア": "Clear settings", "ボーン設定データ全てクリアします。": "Clear all bone setting data.", "（デバッグ版）": "(Debug version)", "-- Aスタンス調整終了": "--A Stance adjustment completed", "-- グループモーフデータ解析": "--Group morph data analysis", "-- 身体剛体設定終了": "--End of body rigid body setting", "exeバージョン": "exe version", "物理設定クリア": "Clear physical settings", "有効な頂点マップが生成できなかった為、処理を終了します": "The process will be terminated because a valid vertex map could not be generated.", "Vroid2Pmx処理実行": "Vroid2Pmx processing execution", "PMX出力": "PMX output", "作者": "author", "連絡先": "contact address", "参照": "reference", "バージョン": "version", "アバターの人格に関する許諾範囲": "Scope of permission regarding the personality of the avatar", "アバターに人格を与えることの許諾範囲": "Scope of permission to give personality to avatars", "このアバターを用いて暴力表現を演じることの許可": "Permission to perform violent expressions using this avatar", "このアバターを用いて性的表現を演じることの許可": "Permission to perform sexual expression using this avatar", "商用利用の許可": "Permission for commercial use", "その他のライセンス条件": "Other license terms", "再配布・改変に関する許諾範囲": "Scope of permission for redistribution / modification", "ライセンスタイプ": "License type", "物理を設定したい場合は、変換後のPmxデータをPmxTailorにかけてください。": "If you want to set the physics, apply the converted Pmx data to PmxTailor.", "アバター情報": "Avatar information", "出力ソフト情報がないため、処理を中断します。": "Processing is interrupted because there is no output software information.", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。": "The processing is interrupted because it is not the vrm data output by VRoid Studio 1.0.x.", "メタ情報がないため、処理を中断します。": "Processing is interrupted because there is no meta information.", "Vroid Studio 正式版(1.0.0)以降でエクスポートされたVrmモデルをPmxモデルに変換します。\n": "Converts Vrm models exported from Vroid Studio official version (1.0.0) or later to Pmx models.\n", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .pmx\n設定可能拡張子: vrm": "The extension of the target model is incorrect.\nInput file extension: .pmx\nConfigurable extension: vrm", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。\n出力元: %s": "The processing is interrupted because it is not the vrm data output by VRoid Studio 1.0.x.\nOutput source: %s", "VRoid Studio ベータ版 で出力されたvrmデータではあるため、処理を中断します。\n正式版でコンバートしてから再度試してください。\n出力元: %s": "Since it is vrm data output by VRoid Studio Beta, processing will be interrupted.\nPlease convert with the official version and try again.\nOutput source: %s", "VRoid Studio 1.x で出力されたvrmデータではないため、結果がおかしくなる可能性があります。\n（結果がおかしくてもサポート対象外となります）\n出力元: %s": "The result may be strange because it is not the vrm data output by VRoid Studio 1.x.\n(Even if the result is strange, it will not be supported)\nOutput source: %s", "val_type in [TYPE_INT, TYPE_UNSIGNED_INT]: %s": "val_type in [TYPE_INT, TYPE_UNSIGNED_INT]: %s", "write_number失敗: type: %s, val: %s, int(val): %s": "write_number failure: type: %s, val: %s, int (val): %s", "髪の反射色がVrmデータになかったため、仮に白色を差し込みます": "Since the reflected color of the hair was not in the Vrm data, temporarily insert white", "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s": "Vroid2Pmx processing ended with an unintended error.\n\n %s", "物理(簡易)を入れる": "Put physics (simple)", "チェックを入れると、物理(簡易)を設定します。\nVRoid Studio で設定された物理をそのまま再現は出来てません。\nPmxTailor で入れた物理に比べて固くなりがちです。": "If checked, physical (simple) will be set.\nThe physics set in VRoid Studio cannot be reproduced as it is.\nIt tends to be harder than the physics put in with PmxTailor.", "-- -- PmxTailor用設定ファイル出力準備終了": "---- Ready to output the configuration file for PmxTailor", "胸(小)": "Bust (small)", "髪(ロング)": "Hair (long)", "-- PmxTailor用設定ファイル出力終了": "--End of output of configuration file for PmxTailor", "髪(ショート)": "Hair (short)", "-- -- PmxTailor用設定ファイル出力準備 (%s)": "---- Preparation for output of configuration file for PmxTailor ( %s)", "単一揺れ物": "Single sway", "VRoid Studioで物理が設定ボーンの変換先が見つかりませんでした。 ボーン名: %s": "The physics set bone conversion destination could not be found in VRoid Studio. Bone name: %s", "既に同じ材質名が登録されているため、元の材質名のまま登録します 変換材質名: %s 元材質名: %s": "Since the same material name has already been registered, register the original material name as it is. Conversion material name: %s Original material name: %s", "-- -- PmxTailor用設定ファイル出力 (%s)": "---- Configuration file output for PmxTailor ( %s)", "髪(アホ毛)": "Hair (ahoge)", "胸(大)": "Bust (large)", "-- -- モーフ調整[%s]": "---- Morph adjustment [ %s]", "-- -- モーフ調整: %s個目": "---- Morph adjustment: %sth", "-- -- 拡張モーフ調整: %s個目": "---- Extended morph adjustment: %sth", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .vroid\n設定可能拡張子: vrm": "The extension of the target model is incorrect.\nInput file extension: .vroid\nConfigurable extension: vrm", "基本色が白ではないため、加算します。": "Since the basic color is not white, add it.", "基本色が白ではないため、加算合成します。 材質名: %s": "Since the basic color is not white, additive synthesis is performed. Material name: %s", "ダブルクリックされました。": "It was double-clicked.", "まだ処理が実行中です。終了してから再度実行してください。": "Processing is still in progress. Please try again after finishing.", "テクスチャの透明部分がUVに含まれているため、エッジ材質を作成します 材質名: %s": "Create an edge material because the UV contains the transparent part of the texture Material name: %s", "-- -- モーフ調整準備": "---- Morph adjustment preparation", "Fcl_EYE_Surprised モーフがなかったため、瞳小モーフ生成をスルーします": "Since there was no Fcl_EYE_Surprised morph, the pupil small morph generation is passed through.", "Fcl_EYE_Surprised モーフがなかったため、瞳大モーフ生成をスルーします": "Since there was no Fcl_EYE_Surprised morph, the pupil size morph generation is passed through.", "Fcl_EYE_Close モーフがなかったため、目隠し頂点モーフ生成をスルーします": "Since there was no Fcl_EYE_Close morph, the blindfolded vertex morph generation is passed through.", "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s": "The physics set in VRoid Studio could not be converted to the settings for PmxTailor. Definition name: %s, Material name: %s, Bone name: %s", "布(ベルベット)": "<PERSON><PERSON>h (velvet)", "-- PmxTailor用設定ファイル出力準備1": "--Preparation for output of configuration file for PmxTailor 1", "-- -- PmxTailor用設定ファイル出力準備1 (%s)": "---- Preparation for output of configuration file for PmxTailor 1 ( %s)", "-- PmxTailor用設定ファイル出力準備2": "--Preparation for output of configuration file for PmxTailor 2", "-- -- PmxTailor用設定ファイル出力準備2 (%s)": "---- Preparation for output of configuration file for PmxTailor 2 ( %s)", "-- 身体剛体準備終了": "--Completion of body rigid body preparation", "-- -- 身体剛体[%s]": "---- Body Rigid Body [ %s]", "舌関連頂点が見つからなかったため、舌分離処理をスキップします": "No tongue-related vertices found, skipping tongue separation processing", "-- Aスタンス・親指調整終了": "-- Finished A stance/thumb adjustment", "出力ソフト情報がないため、処理を中断します。\nvrm1.0でエクスポートした場合、vrm0.0でエクスポートし直してください。": "The process is aborted because there is no output software information. \nIf you exported with vrm1.0, please re-export with vrm0.0."}