# -*- coding: utf-8 -*-
#

import struct
import random
import string
import numpy as np
from module.MMath import MVector2D, MVector3D, MVector4D
from mmd.PmxData import Bdef1, Bdef2, Bdef4
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)


def define_buf_type(componentType: int):
    """定义缓冲区类型"""
    if componentType == 5120:
        return "b", 1
    elif componentType == 5121:
        return "B", 1
    elif componentType == 5122:
        return "h", 2
    elif componentType == 5123:
        return "H", 2
    elif componentType == 5124:
        return "i", 4
    elif componentType == 5125:
        return "I", 4
    return "f", 4


def read_from_accessor(model, accessor_idx: int):
    """
    通过accessor索引从glTF数据中读取值
    https://github.com/ft-lab/Documents_glTF/blob/master/structure.md
    """
    bresult = None
    aidx = 0
    if accessor_idx < len(model.json_data["accessors"]):
        accessor = model.json_data["accessors"][accessor_idx]
        acc_type = accessor["type"]
        if accessor["bufferView"] < len(model.json_data["bufferViews"]):
            buffer = model.json_data["bufferViews"][accessor["bufferView"]]
            logger.debug("accessor: %s, %s", accessor_idx, buffer)

            # 从model中获取buffer_data
            buffer_idx = buffer["buffer"]
            if buffer_idx not in model.buffer_data:
                return []
            buffer_data = model.buffer_data[buffer_idx]

            if "count" in accessor:
                bresult = []
                if acc_type == "VEC3":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = buffer["byteOffset"] + ((buf_num * 3) * n)

                        # Vec3 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)
                        zresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 2))

                        if buf_type == "f":
                            bresult.append(MVector3D(float(xresult[0]), float(yresult[0]), float(zresult[0])))
                        else:
                            bresult.append(MVector3D(int(xresult[0]), int(yresult[0]), int(zresult[0])))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "VEC2":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = buffer["byteOffset"] + ((buf_num * 2) * n)

                        # Vec2 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)

                        bresult.append(MVector2D(float(xresult[0]), float(yresult[0])))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "VEC4":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = buffer["byteOffset"] + ((buf_num * 4) * n)

                        # Vec4 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)
                        zresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 2))
                        wresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 3))

                        if buf_type == "f":
                            bresult.append(
                                MVector4D(
                                    float(xresult[0]), float(yresult[0]), float(zresult[0]), float(wresult[0])
                                )
                            )
                        else:
                            bresult.append(
                                MVector4D(int(xresult[0]), int(yresult[0]), int(zresult[0]), int(wresult[0]))
                            )

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "SCALAR":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = buffer["byteOffset"] + (buf_num * n)
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)

                        if buf_type == "f":
                            bresult.append(float(xresult[0]))
                        else:
                            bresult.append(int(xresult[0]))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

    return bresult


def get_deform_index(
    vertex_idx: int,
    model,
    vertex_pos: MVector3D,
    joint: MVector4D,
    skin_joints: list,
    node_weight: list,
    bone_name_dict: dict,
):
    """获取变形索引"""
    deform = None

    # 获取骨骼索引和权重值
    joint_indices = [int(joint.x()), int(joint.y()), int(joint.z()), int(joint.w())]

    # node_weight[0]是MVector4D对象，包含4个权重值
    weight_vec = node_weight[0]
    weights = [weight_vec.x(), weight_vec.y(), weight_vec.z(), weight_vec.w()]

    # 将glTF节点索引映射到PMX骨骼索引
    def get_pmx_bone_index(joint_idx):
        if joint_idx < len(skin_joints):
            node_idx = skin_joints[joint_idx]
            # 在bone_name_dict中查找对应的PMX骨骼索引
            for node_name, bone_info in bone_name_dict.items():
                if bone_info.get("node_index") == node_idx:
                    return bone_info.get("index", 0)
        return 0  # 默认返回0（根骨骼）

    # 根据权重数量选择变形类型
    non_zero_weights = [w for w in weights if w > 0.001]

    if len(non_zero_weights) == 1:
        # Bdef1
        pmx_bone_idx = get_pmx_bone_index(int(joint.x()))
        deform = Bdef1(pmx_bone_idx)
    elif len(non_zero_weights) == 2:
        # Bdef2
        pmx_bone1_idx = get_pmx_bone_index(int(joint.x()))
        pmx_bone2_idx = get_pmx_bone_index(int(joint.y()))
        weight1 = weights[0]
        deform = Bdef2(pmx_bone1_idx, pmx_bone2_idx, weight1)
    else:
        # Bdef4
        pmx_bone1_idx = get_pmx_bone_index(int(joint.x()))
        pmx_bone2_idx = get_pmx_bone_index(int(joint.y()))
        pmx_bone3_idx = get_pmx_bone_index(int(joint.z()))
        pmx_bone4_idx = get_pmx_bone_index(int(joint.w()))
        weight1 = weights[0]
        weight2 = weights[1]
        weight3 = weights[2]
        weight4 = weights[3]
        deform = Bdef4(pmx_bone1_idx, pmx_bone2_idx, pmx_bone3_idx, pmx_bone4_idx, weight1, weight2, weight3, weight4)

    return deform


def calc_ratio(ratio: float, oldmin: float, oldmax: float, newmin: float, newmax: float):
    """计算比例转换"""
    # https://qastack.jp/programming/929103/convert-a-number-range-to-another-range-maintaining-ratio
    # NewValue = (((OldValue - OldMin) * (NewMax - NewMin)) / (OldMax - OldMin)) + NewMin
    return (((ratio - oldmin) * (newmax - newmin)) / (oldmax - oldmin)) + newmin


def randomname(n) -> str:
    """生成随机名称"""
    return "".join(random.choices(string.ascii_letters + string.digits, k=n))


def calc_intersect_point(p0: np.ndarray, p1: np.ndarray, p_co: np.ndarray, p_no: np.ndarray, epsilon=1e-6):
    """
    计算直线与平面的交点
    https://stackoverflow.com/questions/5666222/3d-line-plane-intersection

    p0, p1: Define the line.
    p_co, p_no: define the plane:
        p_co Is a point on the plane (plane coordinate).
        p_no Is a normal vector defining the plane direction;
             (does not need to be normalized).

    Return a Vector or None (when the intersection can't be found).
    """
    u = p1 - p0
    dot = p_no.dot(u)

    if abs(dot) > epsilon:
        # The factor of the point between p0 -> p1 (0 - 1)
        # if 'fac' is between (0 - 1) the point intersects with the segment.
        # Otherwise:
        #  < 0.0: behind p0.
        #  > 1.0: infront of p1.
        w = p0 - p_co
        fac = -p_no.dot(w) / dot
        u = u * fac
        return p0 + u

    # The segment is parallel to plane.
    return None