# -*- coding: utf-8 -*-
#

import struct
import numpy as np
from module.MMath import MVector2D, MVector3D, MVector4D
from mmd.PmxData import Bdef1, Bdef2, Bdef4
from utils.MLogger import MLogger

logger = MLogger(__name__, level=1)


def define_buf_type(componentType: int):
    """定义缓冲区类型"""
    if componentType == 5120:
        return "b", 1
    elif componentType == 5121:
        return "B", 1
    elif componentType == 5122:
        return "h", 2
    elif componentType == 5123:
        return "H", 2
    elif componentType == 5124:
        return "i", 4
    elif componentType == 5125:
        return "I", 4
    return "f", 4


def read_from_accessor(model, accessor_idx: int, buffer_data=None, offset=0):
    """
    通过accessor索引从glTF数据中读取值
    https://github.com/ft-lab/Documents_glTF/blob/master/structure.md
    """
    bresult = None
    aidx = 0
    if accessor_idx < len(model.json_data["accessors"]):
        accessor = model.json_data["accessors"][accessor_idx]
        acc_type = accessor["type"]
        if accessor["bufferView"] < len(model.json_data["bufferViews"]):
            buffer = model.json_data["bufferViews"][accessor["bufferView"]]
            logger.debug("accessor: %s, %s", accessor_idx, buffer)
            if "count" in accessor:
                bresult = []
                if acc_type == "VEC3":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = offset + buffer["byteOffset"] + ((buf_num * 3) * n)

                        # Vec3 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)
                        zresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 2))

                        if buf_type == "f":
                            bresult.append(MVector3D(float(xresult[0]), float(yresult[0]), float(zresult[0])))
                        else:
                            bresult.append(MVector3D(int(xresult[0]), int(yresult[0]), int(zresult[0])))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "VEC2":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = offset + buffer["byteOffset"] + ((buf_num * 2) * n)

                        # Vec2 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)

                        bresult.append(MVector2D(float(xresult[0]), float(yresult[0])))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "VEC4":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = offset + buffer["byteOffset"] + ((buf_num * 4) * n)

                        # Vec4 / float
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)
                        yresult = struct.unpack_from(buf_type, buffer_data, buf_start + buf_num)
                        zresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 2))
                        wresult = struct.unpack_from(buf_type, buffer_data, buf_start + (buf_num * 3))

                        if buf_type == "f":
                            bresult.append(
                                MVector4D(
                                    float(xresult[0]), float(yresult[0]), float(zresult[0]), float(wresult[0])
                                )
                            )
                        else:
                            bresult.append(
                                MVector4D(int(xresult[0]), int(yresult[0]), int(zresult[0]), int(wresult[0]))
                            )

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                elif acc_type == "SCALAR":
                    buf_type, buf_num = define_buf_type(accessor["componentType"])
                    if accessor_idx % 10 == 0:
                        logger.debug("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

                    for n in range(accessor["count"]):
                        buf_start = offset + buffer["byteOffset"] + (buf_num * n)
                        xresult = struct.unpack_from(buf_type, buffer_data, buf_start)

                        if buf_type == "f":
                            bresult.append(float(xresult[0]))
                        else:
                            bresult.append(int(xresult[0]))

                        aidx += 1

                        if aidx % 5000 == 0:
                            logger.debug("-- -- Accessor[%s/%s/%s][%s]", accessor_idx, acc_type, buf_type, aidx)
                        else:
                            logger.test("-- -- Accessor[%s/%s/%s]", accessor_idx, acc_type, buf_type)

    return bresult


def get_deform_index(
    vertex_idx: int,
    model,
    vertex_pos: MVector3D,
    joint: MVector4D,
    skin_joints: list,
    node_weight: list,
    bone_name_dict: dict,
):
    """获取变形索引"""
    deform = None

    # 获取权重值
    weights = [node_weight[int(joint.x)], node_weight[int(joint.y)], node_weight[int(joint.z)], node_weight[int(joint.w)]]

    # 根据权重数量选择变形类型
    non_zero_weights = [w for w in weights if w > 0.001]

    if len(non_zero_weights) == 1:
        # Bdef1
        bone_idx = int(joint.x)
        if bone_idx < len(skin_joints):
            bone_name = bone_name_dict.get(skin_joints[bone_idx], f"Bone_{bone_idx}")
            deform = Bdef1(bone_idx)
    elif len(non_zero_weights) == 2:
        # Bdef2
        bone1_idx = int(joint.x)
        bone2_idx = int(joint.y)
        weight1 = weights[0]
        deform = Bdef2(bone1_idx, bone2_idx, weight1)
    else:
        # Bdef4
        bone1_idx = int(joint.x)
        bone2_idx = int(joint.y)
        bone3_idx = int(joint.z)
        bone4_idx = int(joint.w)
        weight1 = weights[0]
        weight2 = weights[1]
        weight3 = weights[2]
        deform = Bdef4(bone1_idx, bone2_idx, bone3_idx, bone4_idx, weight1, weight2, weight3)

    return deform