# -*- coding: utf-8 -*-
"""
PmxTailor设置导出模块
"""

import os
import json
from pathlib import Path
from module.MMath import MVector3D
from module.PmxModel import PmxModel
from mmd_human_dance_pose.module.MLogger import MLogger

logger = MLogger(__name__)


class PmxTailorExporter:
    """PmxTailor设置导出器"""
    
    def __init__(self):
        pass
    
    def export_pmxtailor_setting(self, model: PmxModel, setting_dir_path: str):
        """导出PmxTailor设置文件"""
        if (
            "extensions" not in model.json_data
            or "VRM" not in model.json_data["extensions"]
            or "secondaryAnimation" not in model.json_data["extensions"]["VRM"]
            or "boneGroups" not in model.json_data["extensions"]["VRM"]["secondaryAnimation"]
            or "nodes" not in model.json_data
        ):
            return

        # 材質・ボーン・頂点INDEXの対応表を作成
        logger.info("-- PmxTailor用設定ファイル出力準備1")
        bone_materials = {}
        material_bones = {}
        for bone_idx, bone_vidxs in model.vertices.items():
            bone_name = model.bone_indexes.get(bone_idx, None)
            for material_name, vidxs in model.material_vertices.items():
                # 一定以上ウェイトが乗っている場合のみ対象とする
                weighted_vidxs = [
                    vidx
                    for vidx in list(set(vidxs) & set(bone_vidxs))
                    if bone_idx in model.vertex_dict[vidx].deform.get_idx_list(0.3)
                ]

                if len(weighted_vidxs) > 10:
                    if bone_name not in bone_materials:
                        bone_materials[bone_name] = []
                    bone_materials[bone_name].append(material_name)

                    if material_name not in material_bones:
                        material_bones[material_name] = []
                    material_bones[material_name].append(bone_name)

        logger.info("-- PmxTailor用設定ファイル出力準備2")

        # ボーングループの処理
        bone_groups = model.json_data["extensions"]["VRM"]["secondaryAnimation"]["boneGroups"]
        nodes = model.json_data["nodes"]

        pmxtailor_settings = {
            "version": "1.0",
            "model_name": model.name,
            "bone_groups": [],
            "collider_groups": []
        }

        for group_idx, bone_group in enumerate(bone_groups):
            group_name = bone_group.get("comment", f"Group_{group_idx}")
            
            # ボーングループの設定
            group_setting = {
                "name": group_name,
                "bones": [],
                "stiffness": bone_group.get("stiffnessForce", 1.0),
                "gravity_power": bone_group.get("gravityPower", 0.0),
                "gravity_dir": [
                    bone_group.get("gravityDir", {}).get("x", 0.0),
                    bone_group.get("gravityDir", {}).get("y", -1.0),
                    bone_group.get("gravityDir", {}).get("z", 0.0)
                ],
                "drag_force": bone_group.get("dragForce", 0.4),
                "hit_radius": bone_group.get("hitRadius", 0.02)
            }

            # ボーンの処理
            if "bones" in bone_group:
                for bone_idx in bone_group["bones"]:
                    if bone_idx < len(nodes):
                        node = nodes[bone_idx]
                        bone_name = node.get("name", f"bone_{bone_idx}")
                        
                        # PMXのボーン名に変換
                        pmx_bone_name = self._convert_bone_name(bone_name)
                        if pmx_bone_name and pmx_bone_name in model.bones:
                            bone_setting = {
                                "name": pmx_bone_name,
                                "materials": bone_materials.get(pmx_bone_name, [])
                            }
                            group_setting["bones"].append(bone_setting)

            if group_setting["bones"]:
                pmxtailor_settings["bone_groups"].append(group_setting)

        # コライダーグループの処理
        if "colliderGroups" in model.json_data["extensions"]["VRM"]["secondaryAnimation"]:
            collider_groups = model.json_data["extensions"]["VRM"]["secondaryAnimation"]["colliderGroups"]
            
            for collider_group in collider_groups:
                collider_setting = {
                    "name": collider_group.get("comment", "Collider"),
                    "colliders": []
                }

                if "colliders" in collider_group:
                    for collider in collider_group["colliders"]:
                        if "node" in collider:
                            node_idx = collider["node"]
                            if node_idx < len(nodes):
                                node = nodes[node_idx]
                                bone_name = node.get("name", f"bone_{node_idx}")
                                pmx_bone_name = self._convert_bone_name(bone_name)
                                
                                if pmx_bone_name and pmx_bone_name in model.bones:
                                    collider_info = {
                                        "bone": pmx_bone_name,
                                        "radius": collider.get("radius", 0.05),
                                        "offset": [
                                            collider.get("offset", {}).get("x", 0.0),
                                            collider.get("offset", {}).get("y", 0.0),
                                            collider.get("offset", {}).get("z", 0.0)
                                        ]
                                    }
                                    collider_setting["colliders"].append(collider_info)

                if collider_setting["colliders"]:
                    pmxtailor_settings["collider_groups"].append(collider_setting)

        # 設定ファイルを出力
        os.makedirs(setting_dir_path, exist_ok=True)
        setting_file_path = os.path.join(setting_dir_path, "pmxtailor_setting.json")
        
        with open(setting_file_path, "w", encoding="utf-8") as f:
            json.dump(pmxtailor_settings, f, ensure_ascii=False, indent=2)

        logger.info(f"-- PmxTailor用設定ファイル出力完了: {setting_file_path}")

        # 追加の設定ファイル（材質別設定）
        self._export_material_settings(model, setting_dir_path, material_bones)

    def _convert_bone_name(self, vrm_bone_name):
        """VRMボーン名をPMXボーン名に変換"""
        # 簡単な変換テーブル
        bone_name_map = {
            "J_Bip_C_Head": "頭",
            "J_Bip_C_Neck": "首",
            "J_Bip_C_UpperChest": "上半身3",
            "J_Bip_C_Chest": "上半身2",
            "J_Bip_C_Spine2": "上半身",
            "J_Bip_C_Spine": "下半身",
            "J_Bip_C_Hips": "腰",
            "J_Bip_L_Shoulder": "左肩",
            "J_Bip_R_Shoulder": "右肩",
            "J_Bip_L_UpperArm": "左腕",
            "J_Bip_R_UpperArm": "右腕",
            "J_Bip_L_LowerArm": "左ひじ",
            "J_Bip_R_LowerArm": "右ひじ",
            "J_Bip_L_Hand": "左手首",
            "J_Bip_R_Hand": "右手首",
            "J_Bip_L_UpperLeg": "左足",
            "J_Bip_R_UpperLeg": "右足",
            "J_Bip_L_LowerLeg": "左ひざ",
            "J_Bip_R_LowerLeg": "右ひざ",
            "J_Bip_L_Foot": "左足首",
            "J_Bip_R_Foot": "右足首",
        }
        
        return bone_name_map.get(vrm_bone_name, vrm_bone_name)

    def _export_material_settings(self, model: PmxModel, setting_dir_path: str, material_bones: dict):
        """材質別設定を出力"""
        material_settings = {
            "version": "1.0",
            "materials": []
        }

        for material_name, material in model.materials.items():
            material_setting = {
                "name": material_name,
                "english_name": material.english_name,
                "bones": material_bones.get(material_name, []),
                "physics_enabled": True,
                "collision_enabled": True,
                "stiffness": 1.0,
                "damping": 0.1
            }

            # 材質の種類に応じて設定を調整
            if "HAIR" in material_name.upper():
                material_setting["stiffness"] = 0.5
                material_setting["damping"] = 0.2
            elif "CLOTH" in material_name.upper():
                material_setting["stiffness"] = 0.8
                material_setting["damping"] = 0.15
            elif "SKIN" in material_name.upper():
                material_setting["physics_enabled"] = False

            material_settings["materials"].append(material_setting)

        # 材質設定ファイルを出力
        material_file_path = os.path.join(setting_dir_path, "material_settings.json")
        with open(material_file_path, "w", encoding="utf-8") as f:
            json.dump(material_settings, f, ensure_ascii=False, indent=2)

        logger.info(f"-- 材質設定ファイル出力完了: {material_file_path}")
