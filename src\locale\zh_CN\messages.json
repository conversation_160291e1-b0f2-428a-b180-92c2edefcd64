{"（ログあり版）": "（带日志的版本）", "PmxTailor ローカル版": "PmxTailor 本地版", "PmxTailor実行": "运行 PmxTailor", "PmxTailor停止": "停止 PmxTailor", "PMXモデルの指定された材質に物理を設定します。\n": "将物理设置为 PMX 模型的指定材料。\n", "PMXモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "加载 PMX 模型后，在 Para-adjustment 选项卡上设置物理。", "対象モデル": "对象模型", "対象モデルPMXファイルを開く": "打开目标模型 PMX 文件", "変換したいPMXファイルパスを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "指定要转换的 PMX 文件路径。\n您可以通过 D&D 指定，从打开按钮指定，并从历史记录中选择。", "出力対象PMX": "输出目标 PMX", "出力対象PMXファイルを開く": "打开输出目标 PMX 文件", "変換結果PMX出力パスを指定してください。\n対象モデルPMXファイル名に基づいて自動生成されますが、任意のパスに変更することも可能です。": "指定转换结果 PMX 输出路径。\n它是根据目标模型 PMX 文件名自动生成的，但您可以将其更改为任何路径。", "PMXモデルに物理を設定します": "为 PMX 模型设置物理", "ファイル": "文件", "材質を選択して、パラメーターを調整してください。": "选择材料并调整参数。", "材質設定クリア": "清除材料设置", "既に入力されたデータをすべて空にします。": "清空所有已经输入的数据。", "物理設定追加": "添加物理设置", "物理設定フォームをパネルに追加します。": "将物理表单添加到面板。", "パラ調整": "参数调整", "パラ調整タブで材質を選択して、パラメーターを調整してください。\n": "在 Para 调整选项卡上选择材质并调整参数。\n", "※パラ調整タブで変更した値は詳細タブに反映されますが、逆方向には反映されません": "* 参数调整选项卡中更改的值会反映在详细信息选项卡中，但不会反方向显示。", "パラ調整(詳細)": "Para 调整（细节）", "パラ調整タブで選択された材質に既にボーンとウェイトが設定されている場合に、\n": "如果已经为 Para Adjustment 选项卡上选择的材料设置了骨骼和权重，\n", "ボーン構成を指定する事で物理設定（剛体・ジョイント）を設定する事が出来ます。\n": "可以通过指定骨骼配置来设置物理设置（刚体/关节）。\n", "縦方向がボーンの親子関係、横がボーンの並び順で指定してください。\n": "指定骨骼在垂直方向上的父子关系和在水平方向上骨骼的顺序。\n", "（スカートなどは水平方向の並び順、袖などは輪切り縦方向の並び順を横方向に並べてください）\n": "（裙子等，横向排列，袖子等，纵向排列。）\n", "ボーン名を指定すると、その子ボーンを自動設定します。（少しタイムラグがあります）\n": "如果您指定骨骼名称，则将自动设置其子骨骼。 （有一点时间滞后）\n", "水平方向のボーン間が繋がっているかは、ウェイトを見て自動判定します。": "水平方向的骨骼是否连接是通过查看权重自动确定的。", "パラ調整(ボーン)": "Para 调整（骨骼）", "パラ調整タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "参数调整选项卡显示准备开始\n执行文件读取过程。请稍等片刻....", "読み込み処理停止": "停止阅读过程", "-- PMX 頂点読み込み完了": "--PMX 顶点读取完成", "-- PMX 面読み込み完了": "--PMX表面读取完成", "-- PMX テクスチャ読み込み完了": "--PMX纹理加载完成", "-- PMX 材質読み込み完了": "--PMX材料加载完成", "-- PMX ボーン読み込み完了": "--PMX 骨骼读取完成", "-- PMX モーフ読み込み完了": "--PMX变形加载完成", "-- PMX 表示枠読み込み完了": "--PMX显示帧读取完成", "-- PMX 剛体読み込み完了": "--PMX刚体读取完成", "-- PMX ジョイント読み込み完了": "--PMX联合加载完成", "%s%s 読み込み成功: %s": "%s%s 读取成功：%s", "ファイルデータ読み込みが完了しました": "文件数据读取完成", "インポート ...": "进口 ...", "材質設定データをjsonファイルから読み込みます。\nファイル選択ダイアログが開きます。": "从json文件中读取材质设置数据。\n打开文件选择对话框。", "エクスポート ...": "出口 ...", "材質設定データをjsonファイルに出力します。\n出力先を指定できます。": "将材质设置数据输出到json文件中。\n您可以指定输出目的地。", "物理材質 *": "实物材料 *", "物理を設定する材質を選択してください。\n材質全体に物理を設定するため、裾など一部にのみ物理を設定したい場合、材質を一旦分離してください。": "选择要为其设置物理的材料。\n由于物理是为整个材料设置的，如果您只想为下摆等部分设置物理，则将材料分开一次。", "親ボーン *": "父骨骼 *", "材質物理の起点となる親ボーン\nボーン追従剛体を持っているボーンのみが対象となります。\n（指定された親ボーンの子に「○○中心」ボーンを追加して、それを起点に物理を設定します）": "作为材料物理起点的父骨骼\n只有具有骨骼跟随刚体的骨骼才有资格。\n（在指定的父骨骼的子节点上添加一个“○○中心”骨骼，并以此为起点设置物理）", "材質略称 *": "材料缩写 *", "ボーン名などに使用する材質略称を半角6文字 or 全角3文字以内で入力してください。（任意変更可能。その場合は3文字まで）": "在 6 个单字节字符或 3 个双字节字符内输入用于骨骼名称等的材料缩写。 （可任意更改。在这种情况下，最多 3 个字符）", "剛体グループ *": "刚体组 *", "剛体のグループ。初期設定では、自分自身のグループのみ非衝突として設定します。": "一组刚体。默认情况下，只有您自己的组设置为非冲突。", "物理方向": "物理方向", "物理材質の向き(例：左腕側の物理を設定したい場合に「左」を設定して、物理が流れる方向を左方向に伸ばす)": "物理材质的方向（例如：如果要在左臂侧设置物理，则设置“左”，将物理流动的方向向左延伸）", "下": "在下面", "上": "向上", "右": "对", "左": "剩下", "既存設定": "现有设置", "指定された材質に割り当てられている既存物理（ボーン・剛体・ジョイント）がある場合の挙動\nそのまま：処理しない\n": "当存在分配给指定材料的现有物理（骨骼、刚体、关节）时的行为\n原样：不处理\n", "再利用：ボーンとウェイトは既存のものを利用し、剛体とジョイントだけ作り直す\n上書き：ボーン・剛体・ジョイントを削除して作り直す": "重用：使用现有的骨骼和权重，只重新创建刚体和关节\n覆盖：删除骨骼、刚体和关节并重新创建它们。", "そのまま": "照原样", "再利用": "重用", "上書き": "覆盖", "プリセット": "预设", "物理の参考値プリセット": "物理参考值预设", "裏面材質": "背面材质", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "如果有对应物理材质背面的材质，则选择它。\n根据物理材料的骨骼分割分配权重", "検出度": "检测度", "材質内の頂点を検出する時の傾き等の類似度\n値を小さくすると傾きが違っていても検出しやすくなりますが、誤検知が増える可能性があります。": "检测材料中的顶点时的相似性，例如倾斜度\n即使斜率不同，减小该值也更容易检测，但可能会增加误报。", "（0.75）": "(0.75)", "0.5": "0.5", "1": "1", "細かさ": "细度", "材質の物理の細かさ。ボーン・剛体・ジョイントの細かさ等に影響します。": "材料的物理细度。它会影响骨骼、刚体、关节等的精细度。", "（3.4）": "(3.4)", "小": "小的", "大": "大", "質量": "大量的", "材質の質量。剛体の質量・減衰等に影響します。": "材料质量。它影响刚体的质量和阻尼。", "（0.5）": "(0.5)", "軽": "光", "重": "重的", "柔らかさ": "柔软的", "材質の柔らかさ。大きくなるほどすぐに元の形状に戻ります。（減衰が高い）\n剛体の減衰・ジョイントの強さ等に影響します。": "材料的柔软度。它越大，它越早恢复到原来的形状。 （高衰减）\n它影响刚体的阻尼和关节的强度。", "（1.8）": "(1.8)", "柔": "柔软的", "硬": "难的", "張り": "紧张", "材質の形状維持強度。ジョイントの強さ等に影響します。": "材料的保形强度。它会影响关节的强度。", "（1.5）": "(1.5)", "弱": "虚弱的", "強": "力量", "（材質未選択）": "（未选择材料）", "ボーン密度": "骨密度", "縦密度": "垂直密度", "ボーンの縦方向のメッシュに対する密度": "垂直网格的骨骼密度", "横密度": "横向密度", "ボーンの横方向のメッシュに対する密度": "横向网格的骨骼密度", "物理タイプ": "物理类型", "布": "布", "末端剛体": "结束刚体", "末端剛体の質量": "端部刚体质量", "移動減衰": "运动衰减", "末端剛体の移動減衰": "终端刚体的运动阻尼", "回転減衰": "旋转阻尼", "末端剛体の回転減衰": "端部刚体的旋转阻尼", "反発力": "排斥力", "末端剛体の反発力": "终端刚体的排斥力", "摩擦力": "摩擦力", "末端剛体の摩擦力": "端部刚体摩擦力", "係数": "系数", "末端剛体から上の剛体にかけての加算係数": "从终端刚体到上刚体的附加系数", "剛体形状": "刚性体型", "剛体の形状": "刚性体型", "球": "球", "箱": "盒子", "カプセル": "胶囊", "縦ジョイント": "垂直接缝", "有効": "有效的", "縦ジョイントを有効にするか否か": "是否启用垂直关节", "制限係数": "限制因素", "根元ジョイントが末端ジョイントよりどれくらい制限を強くするか。1の場合、全ての段の制限が均一になります。": "根关节比末端关节的限制要多得多。如果为 1，则所有阶段的限制将是统一的。", "移動X(最小)": "移动 X（最小）", "末端縦ジョイントの移動X(最小)": "末端垂直关节 X 的移动（最小）", "移動Y(最小)": "移动 Y（最小）", "末端縦ジョイントの移動Y(最小)": "末端垂直关节 Y 的运动（最小）", "移動Z(最小)": "移动 Z（最小）", "末端縦ジョイントの移動Z(最小)": "末端垂直关节 Z 的运动（最小）", "移動X(最大)": "移动 X（最大）", "末端縦ジョイントの移動X(最大)": "末端垂直关节 X 的运动（最大）", "移動Y(最大)": "移动 Y（最大）", "末端縦ジョイントの移動Y(最大)": "末端垂直关节 Y 的运动（最大）", "移動Z(最大)": "移动 Z（最大）", "末端縦ジョイントの移動Z(最大)": "末端垂直关节 Z 的运动（最大）", "回転X(最小)": "旋转 X（最小）", "末端縦ジョイントの回転X(最小)": "末端纵向接头 X 的旋转（最小）", "回転Y(最小)": "旋转 Y（最小）", "末端縦ジョイントの回転Y(最小)": "端部纵向接头 Y 的旋转（最小）", "回転Z(最小)": "旋转 Z（最小）", "末端縦ジョイントの回転Z(最小)": "端部纵向接头旋转 Z（最小）", "回転X(最大)": "旋转 X（最大）", "末端縦ジョイントの回転X(最大)": "末端纵向接头 X 的旋转（最大）", "回転Y(最大)": "旋转 Y（最大）", "末端縦ジョイントの回転Y(最大)": "末端垂直接头 Y 的旋转（最大）", "回転Z(最大)": "旋转 Z（最大）", "末端縦ジョイントの回転Z(最大)": "末端垂直接头旋转 Z（最大）", "ばね(移動X)": "弹簧（运动X）", "末端縦ジョイントのばね(移動X)": "端部纵向接头弹簧（运动X）", "ばね(移動Y)": "弹簧（运动 Y）", "末端縦ジョイントのばね(移動Y)": "端部纵向接头弹簧（Y运动）", "ばね(移動Z)": "弹簧（运动 Z）", "末端縦ジョイントのばね(移動Z)": "端部纵向接头弹簧（Z 运动）", "ばね(回転X)": "弹簧（X旋转）", "末端縦ジョイントのばね(回転X)": "端部纵向接头弹簧（X 转）", "ばね(回転Y)": "弹簧（Y 旋转）", "末端縦ジョイントのばね(回転Y)": "末端垂直接头弹簧（Y 旋转）", "ばね(回転Z)": "弹簧（Z 轴旋转）", "末端縦ジョイントのばね(回転Z)": "末端垂直接头弹簧（Z 轴旋转）", "横ジョイント": "水平接头", "横ジョイントを有効にするか否か": "是否启用水平关节", "末端横ジョイントの移動X(最小)": "末端侧向关节 X 的运动（最小）", "末端横ジョイントの移動Y(最小)": "末端侧向关节 Y 的运动（最小）", "末端横ジョイントの移動Z(最小)": "末端侧向关节 Z 的运动（最小）", "末端横ジョイントの移動X(最大)": "末端侧向关节 X 的运动（最大）", "末端横ジョイントの移動Y(最大)": "末端侧向关节 Y 的运动（最大）", "末端横ジョイントの移動Z(最大)": "末端侧向关节 Z 的运动（最大）", "末端横ジョイントの回転X(最小)": "末端侧向关节 X 的旋转（最小）", "末端横ジョイントの回転Y(最小)": "末端侧向关节 Y 的旋转（最小）", "末端横ジョイントの回転Z(最小)": "末端侧向关节 Z 的旋转（最小）", "末端横ジョイントの回転X(最大)": "末端侧向关节 X 的旋转（最大）", "末端横ジョイントの回転Y(最大)": "末端侧向关节 Y 的旋转（最大）", "末端横ジョイントの回転Z(最大)": "末端侧向关节 Z 的旋转（最大）", "末端横ジョイントのばね(移動X)": "端侧关节弹簧（运动X）", "末端横ジョイントのばね(移動Y)": "端侧关节弹簧（Y运动）", "末端横ジョイントのばね(移動Z)": "末端横向接头的弹簧（运动Z）", "末端横ジョイントのばね(回転X)": "端侧关节弹簧（X 旋转）", "末端横ジョイントのばね(回転Y)": "端侧关节弹簧（Y 旋转）", "末端横ジョイントのばね(回転Z)": "端侧关节弹簧（Z 轴旋转）", "斜めジョイント": "对角接头", "斜めジョイントを有効にするか否か": "是否启用对角关节", "末端斜めジョイントの移動X(最小)": "末端对角关节 X 的运动（最小）", "末端斜めジョイントの移動Y(最小)": "末端对角关节 Y 的运动（最小）", "末端斜めジョイントの移動Z(最小)": "末端对角关节 Z 的运动（最小）", "末端斜めジョイントの移動X(最大)": "末端对角关节 X 的运动（最大）", "末端斜めジョイントの移動Y(最大)": "末端对角关节 Y 的运动（最大）", "末端斜めジョイントの移動Z(最大)": "末端对角关节 Z 的运动（最大）", "末端斜めジョイントの回転X(最小)": "对角端关节 X 的旋转（最小）", "末端斜めジョイントの回転Y(最小)": "对角端关节 Y 的旋转（最小）", "末端斜めジョイントの回転Z(最小)": "对角端关节 Z 的旋转（最小）", "末端斜めジョイントの回転X(最大)": "对角端关节 X 的旋转（最大）", "末端斜めジョイントの回転Y(最大)": "对角端关节 Y 的旋转（最大）", "末端斜めジョイントの回転Z(最大)": "对角端关节 Z 的旋转（最大）", "末端斜めジョイントのばね(移動X)": "对角端接头弹簧（运动X）", "末端斜めジョイントのばね(移動Y)": "对角端接头弹簧（Y运动）", "末端斜めジョイントのばね(移動Z)": "对角端接头的弹簧（运动Z）", "末端斜めジョイントのばね(回転X)": "对角端接头弹簧（旋转X）", "末端斜めジョイントのばね(回転Y)": "对角端接头弹簧（Y 旋转）", "末端斜めジョイントのばね(回転Z)": "对角端接头弹簧（Z 轴旋转）", "逆ジョイント": "反向接头", "逆ジョイントを有効にするか否か": "是否启用反向关节", "末端逆ジョイントの移動X(最小)": "末端反向关节 X 的运动（最小）", "末端逆ジョイントの移動Y(最小)": "末端反向关节 Y 的运动（最小）", "末端逆ジョイントの移動Z(最小)": "末端反向关节 Z 的运动（最小）", "末端逆ジョイントの移動X(最大)": "末端反向关节 X 的运动（最大）", "末端逆ジョイントの移動Y(最大)": "末端反向关节 Y 的运动（最大）", "末端逆ジョイントの移動Z(最大)": "末端反向关节 Z 的运动（最大）", "末端逆ジョイントの回転X(最小)": "末端反向接头 X 的旋转（最小）", "末端逆ジョイントの回転Y(最小)": "末端反向接头 Y 的旋转（最小）", "末端逆ジョイントの回転Z(最小)": "末端反向接头旋转 Z（最小）", "末端逆ジョイントの回転X(最大)": "末端反向接头 X 的旋转（最大）", "末端逆ジョイントの回転Y(最大)": "末端反向接头 Y 的旋转（最大）", "末端逆ジョイントの回転Z(最大)": "末端反向接头 Z 的旋转（最大）", "末端逆ジョイントのばね(移動X)": "末端反向关节弹簧（运动X）", "末端逆ジョイントのばね(移動Y)": "末端反向关节弹簧（Y运动）", "末端逆ジョイントのばね(移動Z)": "末端反向关节弹簧（Z 运动）", "末端逆ジョイントのばね(回転X)": "末端反向接头弹簧（旋转 X）", "末端逆ジョイントのばね(回転Y)": "末端反向接头弹簧（旋转 Y）", "末端逆ジョイントのばね(回転Z)": "末端反向接头弹簧（Z 轴旋转）", "ボーン設定データをcsvファイルから読み込みます。\nファイル選択ダイアログが開きます。": "从 csv 文件中读取骨骼设置数据。\n打开文件选择对话框。", "ボーン設定データをcsvファイルに出力します。\n出力先を指定できます。": "将骨骼设置数据输出到 csv 文件。\n您可以指定输出目的地。", "パラ調整(詳細)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "参数调整（详情）选项卡显示准备开始\n执行文件读取过程。请稍等片刻....", "PmxTailor変換処理実行": "PmxTailor 转换流程执行", "元モデル": "前模特", "材質": "材料", "剛体グループ": "刚体组", "【%s】頂点マップ生成": "[%s] 顶点贴图生成", "%s: 面の抽出": "%s：人脸提取", "%s: 面の抽出準備①": " %s：表面提取准备①", "%s: 面の抽出準備②": " %s：表面提取准备②", "%s: 相対頂点マップの生成": " %s: 相对顶点贴图生成", "-- 面: %s個目:終了": "--Surface: %sth: 结束", "%s: 絶対頂点マップの生成": " %s：绝对顶点贴图生成", "-- 絶対頂点マップ: %s個目: ---------": "--绝对顶点贴图： %sth：---------", "-- 絶対頂点マップ: %s個目:終了 ---------": "--绝对顶点贴图： %sth: End ----------", "【%s】ボーン生成": "[%s] 骨生成", "【%s(No.%s)】ウェイト分布": "[%s (No. %s)] 重量分布", "-- 頂点ウェイト: %s個目:終了": "--顶点权重: %sth: End", "【%s(No.%s)】剛体生成": "[%s(No.%s)]刚体生成", "-- 剛体: %s個目:終了": "--刚体：%……结束", "【%s(No.%s)】ジョイント生成": "[%s (No. %s)] 联合生成", "-- ジョイント: %s個目:終了": "--Joint: %sth: 结束", "PMX出力開始": "PMX 输出开始", "-- 頂点データ出力終了(%s)": "--顶点数据输出结束(%s)", "-- 面データ出力終了(%s)": "--表面数据输出结束(%s)", "-- テクスチャデータ出力終了(%s)": "--纹理数据输出结束（%s）", "-- 材質データ出力終了(%s)": "--材料数据输出结束(%s)", "-- ボーンデータ出力終了(%s)": "--骨骼数据输出端(%s)", "-- モーフデータ出力終了(%s)": "-- 变形数据输出结束（ %s）", "-- 表示枠データ出力終了(%s)": "--显示帧数据输出端(%s)", "-- 剛体データ出力終了(%s)": "--刚体数据输出端(%s)", "-- ジョイントデータ出力終了(%s)": "--关节数据输出结束(%s)", "出力終了: %s": "输出结束： %s", "PmxTailor変換処理が意図せぬエラーで終了しました。\n\n%s": "PmxTailor 转换过程以意外错误结束。\n\n%s", "\n処理時間: {0}": "\n处理时间：{0}", "\n処理時間: 05s": "\n处理时间：05s", "既存物理を再利用する場合、「パラ調整(ボーン)」画面でボーン並び順を指定してください。": "重用现有物理时，请在“Para 调整（骨骼）”屏幕上指定骨骼顺序。", "有効な物理設定が1件も設定されていません。\nモデルを選択しなおした場合、物理設定は初期化されます。": "没有设置有效的物理设置。\n如果您重新选择模型，物理设置将被初始化。", "【%s】残ウェイト分布": "[%s] 剩余重量分布", "-- 残頂点ウェイト: %s個目:終了": "--剩余顶点权重: %sth: End", "\n処理時間: 04s": "\n处理时间：04s", "\n処理時間: 08s": "\n处理时间：08s", "ボーン設定CSVを読み込む": "读取骨骼设置 CSV", "ボーン設定CSVのインポートに成功しました \n{0}": "成功导入骨骼设置CSV\n{0}", "ボーン設定CSVのインポートに成功しました \n%s": "成功导入骨骼设置CSV\n%s", "パラ調整(ボーン)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "Para 调整（骨骼）选项卡显示准备开始\n执行文件读取过程。请稍等片刻....", "1番目の対象モデルが見つかりませんでした。\n入力パス: %s": "未找到第一个目标模型。\n输入路径： %s", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。": "无法打开“{0}”选项卡，因为“文件”选项卡中未指定目标模型文件路径。", "\n既に指定済みの場合、現在読み込み中の可能性があります。": "\n如果已指定，则当前可能正在加载。", "\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "\n出现“■ 读取成功”日志后，打开“{0}”选项卡。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "无法打开“{0}”选项卡，因为“文件”选项卡中未指定目标模型文件路径。\n如果已指定，则当前可能正在加载。\n出现“■ 读取成功”日志后，打开“{0}”选项卡。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「%s」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「%s」タブを開いてください。": "无法打开“%s”选项卡，因为“文件”选项卡中未指定目标模型文件路径。\n如果已指定，则当前可能正在加载。\n出现“■ 读取成功”日志后，打开“ %s”选项卡。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「パラ調整」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「パラ調整」タブを開いてください。": "由于“文件”选项卡中未指定目标模型文件路径，因此无法打开“参数调整”选项卡。\n如果已指定，则当前可能正在加载。\n出现“■ 读取成功”日志后，打开“参数调整”选项卡。", "\n処理時間: %s": "\n处理时间： %s", "ボーン設定CSVを保存する": "保存骨骼设置 CSV", "ボーン設定CSVのエクスポートに成功しました \n{0}": "骨骼设置 CSV 成功导出\n{0}", "「パラ調整(ボーン)」画面でボーン並び順を指定した場合、既存物理は「再利用」を指定してください。": "如果在“Para Adjustment (Bone)”屏幕上指定骨骼顺序，请为现有物理指定“Reuse”。", "【%s】既存材質削除": "[%s] 删除现有材料", "%s: 削除対象抽出": " %s：要删除的提取", "%s: 削除実行": "%s：删除执行", "%s: INDEX振り直し": " %s: INDEX 重投", "%s: INDEX再割り当て": " %s: INDEX 重新分配", "【%s】ボーンマップ生成": "[%s] 骨骼图生成", "【%s】剛体生成": "[%s] 刚体生成", "【%s】ジョイント生成": "[%s] 联合生成", "履歴": "历史", "これまで指定された対象モデルを再指定できます。": "您可以重新指定之前指定的目标模型。", "開く": "打开", "ファイルを選んでダブルクリック、またはOKボタンをクリックしてください。": "选择文件并双击它，或单击确定按钮。", "{0}番目の": "{0} 个", "【%s】裏面ウェイト分布": "[%s] 背面重量分布", "-- 裏頂点ウェイト: %s個目:終了": "--back vertex weight: %sth: End", "成功": "成功", "髪": "头发", "物理": "物理", "Vroid2Pmx ローカル版": "Vroid2Pmx 本地版本", "Vroid2Pmx実行": "运行 Vroid2Pmx", "Vroid2Pmx停止": "停止 Vroid2Pmx", "Vrmモデルの指定された材質に物理を設定します。\n": "将物理设置为 Vrm 模型的指定材料。\n", "Vrmモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "加载 Vrm 模型后，在 Para-adjustment 选项卡上设置物理。", "対象モデルVrmファイルを開く": "打开目标模型 Vrm 文件", "変換したいVrmファイルパスを指定してください\nVroid Studio 正式版(1.0.0)以降のみ対応しています。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "指定要转换的Vrm文件路径\n仅支持 Vroid Studio 正式版 (1.0.0) 或更高版本。\n您可以通过 D&D 指定，从打开按钮指定，并从历史记录中选择。", "Vrmモデルに物理を設定します": "在 Vrm 模型上设置物理", "Vroid2Pmx処理実行\n------------------------\nexeバージョン: 1.00.00_β19（ログあり版）\n　元モデル: Vroidサンプルメイド.vrm\n": "Vroid2Pmx 处理执行\n------------------------\nexe 版本：1.00.00_β19（带日志的版本）\n原始模型：Vroid 样本 maid.vrm\n", "-- JSON出力終了": "--结束JSON输出", "-- テクスチャデータ解析終了": "--纹理数据分析结束", "Vroid2Pmx実行処理を中断します。": "Vroid2Pmx 执行处理被中断。", "-- -- Accessor[%s/%s/%s]": "---- 存取器 [%s/%s/%s]", "-- -- Accessor[%s/%s/%s][%s]": "---- 存取器 [ %s / %s / %s] [ %s]", "物理を設定する材質を選択してください。\n裾など一部にのみ物理を設定したい場合、頂点データCSVを指定してください。": "选择要为其设置物理的材料。\n如果只想为下摆等部分设置物理，请指定顶点数据 CSV。", "対象頂点CSV": "目标顶点 CSV", "対象頂点CSVファイルを開く": "打开目标顶点 CSV 文件", "材質の中で物理を割り当てたい頂点を絞り込みたい場合、PmxEditorで頂点リストを選択できるようにして保存した頂点CSVファイルを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "如果要缩小材质中要为其分配物理场的顶点，请指定保存的顶点 CSV 文件，以便您可以在 PmxEditor 中选择顶点列表。\n您可以通过 D&D 指定，从打开按钮指定，并从历史记录中选择。", "これまで指定された対象頂点CSVを再指定できます。": "您可以重新指定之前指定的目标顶点 CSV。", "%s: 面の抽出準備③": " %s：表面提取准备③", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質の最も近い頂点ウェイトを転写します": "如果有对应物理材质背面的材质，则选择它。\n传递物理材质最接近的顶点权重", "裾材質": "下摆材料", "物理材質の裾にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "如果有对应物理材料下摆的材料，则选择它。\n根据物理材料的骨骼分割分配权重", "【%s】裾ウェイト分布": "[%s] 下摆重量分布", "Pmxモデルに物理を設定します": "为 Pmx 模型设置物理", "材質を選択して、パラメーターを調整してください。\nスライダーパラメーターで調整した設定に基づいて詳細タブ内のMMD物理パラメーターを変更します。\n物理を再利用したい場合は、ボーンパネルでボーンの並び順を指定してください。": "选择材料并调整参数。\n根据使用滑块参数调整的设置更改“详细信息”选项卡中的 MMD 物理参数。\n如果要重用物理，请在骨骼面板中指定骨骼的顺序。", "親ボーン *　": "父骨骼 *", "裾材質　　": "下摆材料", "裏面材質　": "背面材质", "VrmモデルをPmxモデルに変換します。\n": "将 Vrm 模型转换为 Pmx 模型。\n", "物理を変えたい場合は、変換後のPmxデータをPmxTailorにかけてください。": "如果要更改物理，请将转换后的 Pmx 数据应用到 PmxTailor。", "VrmモデルをPmxモデルに変換します": "将 Vrm 模型转换为 Pmx 模型", "Vroid2Pmx処理実行\n------------------------\nexeバージョン: 1.00.00_β21（ログあり版）\n　元モデル: Vroidサンプルメイド.vrm\n": "Vroid2Pmx 处理执行\n------------------------\nexe 版本：1.00.00_β21（带日志的版本）\n原始模型：Vroid 样本 maid.vrm\n", "-- 頂点データ解析[%s]": "--顶点数据分析[%s]", "-- 面・材質データ解析[%s-%s]": "--表面/材料数据分析[%s-%s]", "-- ボーンデータ解析終了": "--骨骼数据分析完成", "-- 頂点・面・材質データ解析終了": "--顶点/面/材质数据分析结束", "-- ボーンデータ調整終了": "--骨骼数据调整完成", "設定クリア": "清除设置", "ボーン設定データ全てクリアします。": "清除所有骨骼设置数据。", "（デバッグ版）": "（调试版）", "Vroid2Pmx処理実行\n------------------------\nexeバージョン: 1.00.00_β22（ログあり版）\n　元モデル: Vroidサンプルメイド.vrm\n": "Vroid2Pmx 处理执行\n------------------------\nexe 版本：1.00.00_β22（带日志的版本）\n原始模型：Vroid 样本 maid.vrm\n", "-- Aスタンス調整終了": "--A 姿态调整完成", "-- グループモーフデータ解析": "--群体形态数据分析", "-- 身体剛体設定終了": "--body刚体设置结束", "exeバージョン": "exe版本", "物理設定クリア": "清除物理设置", "有効な頂点マップが生成できなかった為、処理を終了します": "由于无法生成有效的顶点映射，该过程将终止。", "Vroid2Pmx処理実行": "Vroid2Pmx 处理执行", "PMX出力": "PMX输出", "作者": "作者", "連絡先": "联系地址", "参照": "参考", "バージョン": "版本", "アバターの人格に関する許諾範囲": "关于头像的个性的许可范围", "アバターに人格を与えることの許諾範囲": "赋予头像个性的权限范围", "このアバターを用いて暴力表現を演じることの許可": "允许使用此头像进行暴力表达", "このアバターを用いて性的表現を演じることの許可": "允许使用此头像进行性表达", "商用利用の許可": "商业用途许可", "その他のライセンス条件": "其他许可条款", "再配布・改変に関する許諾範囲": "重新分发/修改的许可范围", "ライセンスタイプ": "许可证类型", "物理を設定したい場合は、変換後のPmxデータをPmxTailorにかけてください。": "如果要设置物理，请将转换后的 Pmx 数据应用到 PmxTailor。", "アバター情報": "头像信息", "出力ソフト情報がないため、処理を中断します。": "由于没有输出软件信息，处理被中断。", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。": "处理中断，因为不是VRoid Studio 1.0.x输出的vrm数据。", "メタ情報がないため、処理を中断します。": "由于没有元信息，处理被中断。", "Vroid Studio 正式版(1.0.0)以降でエクスポートされたVrmモデルをPmxモデルに変換します。\n": "将从 Vroid Studio 正式版（1.0.0）或更高版本导出的 Vrm 模型转换为 Pmx 模型。\n", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .pmx\n設定可能拡張子: vrm": "目标模型的扩展名不正确。\n输入文件扩展名：.pmx\n可配置扩展名：vrm", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。\n出力元: %s": "由于不是VRoid Studio 1.0.x输出的vrm数据，处理中断。\n输出源：%s", "VRoid Studio ベータ版 で出力されたvrmデータではあるため、処理を中断します。\n正式版でコンバートしてから再度試してください。\n出力元: %s": "由于是VRoid Studio Beta输出的vrm数据，处理会中断。\n请转换成正式版再试。\n输出源：%s", "VRoid Studio 1.x で出力されたvrmデータではないため、結果がおかしくなる可能性があります。\n（結果がおかしくてもサポート対象外となります）\n出力元: %s": "结果可能很奇怪，因为它不是VRoid Studio 1.x输出的vrm数据。\n（即使结果很奇怪，也不会支持）\n输出源：%s", "val_type in [TYPE_INT, TYPE_UNSIGNED_INT]: %s": "[TYPE_INT, TYPE_UNSIGNED_INT] 中的 val_type: %s", "write_number失敗: type: %s, val: %s, int(val): %s": "write_number 失败：类型： %s，val： %s，int (val)： %s", "髪の反射色がVrmデータになかったため、仮に白色を差し込みます": "由于头发的反射颜色不在Vrm数据中，暂时插入白色", "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s": "Vroid2Pmx 处理以意外错误结束。\n\n%s", "物理(簡易)を入れる": "放物理（简单）", "チェックを入れると、物理(簡易)を設定します。\nVRoid Studio で設定された物理をそのまま再現は出来てません。\nPmxTailor で入れた物理に比べて固くなりがちです。": "如果选中，将设置物理（简单）。\nVRoid Studio 中的物理设置无法按原样复制。\n它往往比 PmxTailor 中的物理更难。", "-- -- PmxTailor用設定ファイル出力準備終了": "---- 准备输出 PmxTailor 的配置文件", "髪(ショート)": "头发（短）", "髪(ロング)": "头发（長）", "髪(アホ毛)": "头发 (ahoge)", "胸(小)": "胸（小）", "胸(大)": "胸（大）", "単一揺れ物": "单摆", "布(コットン)": "布（棉）", "布(シルク)": "布（丝绸）", "布(ベルベッド)": "布（铃床）", "布(レザー)": "布（皮革）", "布(デニム)": "布料（牛仔布）", "-- PmxTailor用設定ファイル出力終了": "--PmxTailor 配置文件输出结束", "-- -- PmxTailor用設定ファイル出力準備 (%s)": "---- 准备输出 PmxTailor 的配置文件 (%s)", "VRoid Studioで物理が設定ボーンの変換先が見つかりませんでした。 ボーン名: %s": "在 VRoid Studio 中找不到物理集骨骼转换目标。骨骼名称：%s", "既に同じ材質名が登録されているため、元の材質名のまま登録します 変換材質名: %s 元材質名: %s": "由于已经注册了相同的材质名称，所以直接注册原始材质名称转换材质名称：%s 原始材质名称：%s", "-- -- PmxTailor用設定ファイル出力 (%s)": "---- PmxTailor 的配置文件输出 (%s)", "-- -- モーフ調整[%s]": "---- 变形调整 [%s]", "-- -- モーフ調整: %s個目": "---- 变形调整：%sth", "-- -- 拡張モーフ調整: %s個目": "---- 扩展变形调整：%sth", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .vroid\n設定可能拡張子: vrm": "目标模型的扩展不正确。\n输入文件扩展名：.vroid\n可配置扩展：vrm", "基本色が白ではないため、加算します。": "由于基础颜色不是白色，添加它。", "基本色が白ではないため、加算合成します。 材質名: %s": "由于基本颜色不是白色，因此执行加法合成。材料名称：%s", "ダブルクリックされました。": "它被双击了。", "まだ処理が実行中です。終了してから再度実行してください。": "处理仍在进行中。请完成后重试。", "テクスチャの透明部分がUVに含まれているため、エッジ材質を作成します 材質名: %s": "创建边缘材质，因为UV包含纹理的透明部分材质名称：%s", "-- -- モーフ調整準備": "---- 变形调整准备", "Fcl_EYE_Surprised モーフがなかったため、瞳小モーフ生成をスルーします": "由于没有 Fcl_EYE_Surprise 变形，因此通过了瞳孔小变形生成。", "Fcl_EYE_Surprised モーフがなかったため、瞳大モーフ生成をスルーします": "由于没有 Fcl_EYE_Surprise 变形，因此通过了瞳孔大小变形生成。", "Fcl_EYE_Close モーフがなかったため、目隠し頂点モーフ生成をスルーします": "由于没有 Fcl_EYE_Close 变形，蒙眼的顶点变形生成通过。", "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s": "VRoid Studio 中的物理设置无法转换为 PmxTailor 的设置。定义名称：%s，材质名称：%s，骨骼名称：%s", "布(ベルベット)": "布（丝绒）", "-- PmxTailor用設定ファイル出力準備1": "-- 准备输出 PmxTailor 1 的配置文件", "-- -- PmxTailor用設定ファイル出力準備1 (%s)": "---- 准备输出 PmxTailor 1 的配置文件 (%s)", "-- PmxTailor用設定ファイル出力準備2": "-- 准备输出 PmxTailor 2 的配置文件", "-- -- PmxTailor用設定ファイル出力準備2 (%s)": "---- 准备输出 PmxTailor 2 的配置文件 (%s)", "-- 身体剛体準備終了": "--完成刚体刚体准备", "-- -- 身体剛体[%s]": "---- Body 刚体 [%s]", "舌関連頂点が見つからなかったため、舌分離処理をスキップします": "未找到舌相关顶点，跳过舌分离处理", "-- Aスタンス・親指調整終了": "-- 完成了姿势/拇指调整", "出力ソフト情報がないため、処理を中断します。\nvrm1.0でエクスポートした場合、vrm0.0でエクスポートし直してください。": "由于没有输出软件信息，进程被中止。 \n如果您已使用vrm1.0 导出，请使用 vrm0.0 重新导出。"}