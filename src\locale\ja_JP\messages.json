{"（ログあり版）": "（ログあり版）", "PmxTailor ローカル版": "PmxTailor ローカル版", "PmxTailor実行": "PmxTailor実行", "PmxTailor停止": "PmxTailor停止", "PMXモデルの指定された材質に物理を設定します。\n": "PMXモデルの指定された材質に物理を設定します。\n", "PMXモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "PMXモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。", "対象モデル": "対象モデル", "対象モデルPMXファイルを開く": "対象モデルPMXファイルを開く", "変換したいPMXファイルパスを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "変換したいPMXファイルパスを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。", "出力対象PMX": "出力対象PMX", "出力対象PMXファイルを開く": "出力対象PMXファイルを開く", "変換結果PMX出力パスを指定してください。\n対象モデルPMXファイル名に基づいて自動生成されますが、任意のパスに変更することも可能です。": "変換結果PMX出力パスを指定してください。\n対象モデルPMXファイル名に基づいて自動生成されますが、任意のパスに変更することも可能です。", "PMXモデルに物理を設定します": "PMXモデルに物理を設定します", "ファイル": "ファイル", "材質を選択して、パラメーターを調整してください。": "材質を選択して、パラメーターを調整してください。", "材質設定クリア": "材質設定クリア", "既に入力されたデータをすべて空にします。": "既に入力されたデータをすべて空にします。", "物理設定追加": "物理設定追加", "物理設定フォームをパネルに追加します。": "物理設定フォームをパネルに追加します。", "パラ調整": "パラ調整", "パラ調整タブで材質を選択して、パラメーターを調整してください。\n": "パラ調整タブで材質を選択して、パラメーターを調整してください。\n", "※パラ調整タブで変更した値は詳細タブに反映されますが、逆方向には反映されません": "※パラ調整タブで変更した値は詳細タブに反映されますが、逆方向には反映されません", "パラ調整(詳細)": "パラ調整(詳細)", "パラ調整タブで選択された材質に既にボーンとウェイトが設定されている場合に、\n": "パラ調整タブで選択された材質に既にボーンとウェイトが設定されている場合に、\n", "ボーン構成を指定する事で物理設定（剛体・ジョイント）を設定する事が出来ます。\n": "ボーン構成を指定する事で物理設定（剛体・ジョイント）を設定する事が出来ます。\n", "縦方向がボーンの親子関係、横がボーンの並び順で指定してください。\n": "縦方向がボーンの親子関係、横がボーンの並び順で指定してください。\n", "（スカートなどは水平方向の並び順、袖などは輪切り縦方向の並び順を横方向に並べてください）\n": "（スカートなどは水平方向の並び順、袖などは輪切り縦方向の並び順を横方向に並べてください）\n", "ボーン名を指定すると、その子ボーンを自動設定します。（少しタイムラグがあります）\n": "ボーン名を指定すると、その子ボーンを自動設定します。（少しタイムラグがあります）\n", "水平方向のボーン間が繋がっているかは、ウェイトを見て自動判定します。": "水平方向のボーン間が繋がっているかは、ウェイトを見て自動判定します。", "パラ調整(ボーン)": "パラ調整(ボーン)", "パラ調整タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "パラ調整タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....", "読み込み処理停止": "読み込み処理停止", "-- PMX 頂点読み込み完了": "-- PMX 頂点読み込み完了", "-- PMX 面読み込み完了": "-- PMX 面読み込み完了", "-- PMX テクスチャ読み込み完了": "-- PMX テクスチャ読み込み完了", "-- PMX 材質読み込み完了": "-- PMX 材質読み込み完了", "-- PMX ボーン読み込み完了": "-- PMX ボーン読み込み完了", "-- PMX モーフ読み込み完了": "-- PMX モーフ読み込み完了", "-- PMX 表示枠読み込み完了": "-- PMX 表示枠読み込み完了", "-- PMX 剛体読み込み完了": "-- PMX 剛体読み込み完了", "-- PMX ジョイント読み込み完了": "-- PMX ジョイント読み込み完了", "%s%s 読み込み成功: %s": "%s%s 読み込み成功: %s", "ファイルデータ読み込みが完了しました": "ファイルデータ読み込みが完了しました", "インポート ...": "インポート ...", "材質設定データをjsonファイルから読み込みます。\nファイル選択ダイアログが開きます。": "材質設定データをjsonファイルから読み込みます。\nファイル選択ダイアログが開きます。", "エクスポート ...": "エクスポート ...", "材質設定データをjsonファイルに出力します。\n出力先を指定できます。": "材質設定データをjsonファイルに出力します。\n出力先を指定できます。", "物理材質 *": "物理材質 *", "物理を設定する材質を選択してください。\n材質全体に物理を設定するため、裾など一部にのみ物理を設定したい場合、材質を一旦分離してください。": "物理を設定する材質を選択してください。\n材質全体に物理を設定するため、裾など一部にのみ物理を設定したい場合、材質を一旦分離してください。", "親ボーン *": "親ボーン *", "材質物理の起点となる親ボーン\nボーン追従剛体を持っているボーンのみが対象となります。\n（指定された親ボーンの子に「○○中心」ボーンを追加して、それを起点に物理を設定します）": "材質物理の起点となる親ボーン\nボーン追従剛体を持っているボーンのみが対象となります。\n（指定された親ボーンの子に「○○中心」ボーンを追加して、それを起点に物理を設定します）", "材質略称 *": "材質略称 *", "ボーン名などに使用する材質略称を半角6文字 or 全角3文字以内で入力してください。（任意変更可能。その場合は3文字まで）": "ボーン名などに使用する材質略称を半角6文字 or 全角3文字以内で入力してください。（任意変更可能。その場合は3文字まで）", "剛体グループ *": "剛体グループ *", "剛体のグループ。初期設定では、自分自身のグループのみ非衝突として設定します。": "剛体のグループ。初期設定では、自分自身のグループのみ非衝突として設定します。", "物理方向": "物理方向", "物理材質の向き(例：左腕側の物理を設定したい場合に「左」を設定して、物理が流れる方向を左方向に伸ばす)": "物理材質の向き(例：左腕側の物理を設定したい場合に「左」を設定して、物理が流れる方向を左方向に伸ばす)", "下": "下", "上": "上", "右": "右", "左": "左", "既存設定": "既存設定", "指定された材質に割り当てられている既存物理（ボーン・剛体・ジョイント）がある場合の挙動\nそのまま：処理しない\n": "指定された材質に割り当てられている既存物理（ボーン・剛体・ジョイント）がある場合の挙動\nそのまま：処理しない\n", "再利用：ボーンとウェイトは既存のものを利用し、剛体とジョイントだけ作り直す\n上書き：ボーン・剛体・ジョイントを削除して作り直す": "再利用：ボーンとウェイトは既存のものを利用し、剛体とジョイントだけ作り直す\n上書き：ボーン・剛体・ジョイントを削除して作り直す", "そのまま": "そのまま", "再利用": "再利用", "上書き": "上書き", "プリセット": "プリセット", "物理の参考値プリセット": "物理の参考値プリセット", "布(コットン)": "布(コットン)", "布(シルク)": "布(シルク)", "布(ベルベッド)": "布(ベルベッド)", "布(レザー)": "布(レザー)", "布(デニム)": "布(デニム)", "裏面材質": "裏面材質", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます", "検出度": "検出度", "材質内の頂点を検出する時の傾き等の類似度\n値を小さくすると傾きが違っていても検出しやすくなりますが、誤検知が増える可能性があります。": "材質内の頂点を検出する時の傾き等の類似度\n値を小さくすると傾きが違っていても検出しやすくなりますが、誤検知が増える可能性があります。", "（0.75）": "（0.75）", "0.5": "0.5", "1": "1", "細かさ": "細かさ", "材質の物理の細かさ。ボーン・剛体・ジョイントの細かさ等に影響します。": "材質の物理の細かさ。ボーン・剛体・ジョイントの細かさ等に影響します。", "（3.4）": "（3.4）", "小": "小", "大": "大", "質量": "質量", "材質の質量。剛体の質量・減衰等に影響します。": "材質の質量。剛体の質量・減衰等に影響します。", "（0.5）": "（0.5）", "軽": "軽", "重": "重", "柔らかさ": "柔らかさ", "材質の柔らかさ。大きくなるほどすぐに元の形状に戻ります。（減衰が高い）\n剛体の減衰・ジョイントの強さ等に影響します。": "材質の柔らかさ。大きくなるほどすぐに元の形状に戻ります。（減衰が高い）\n剛体の減衰・ジョイントの強さ等に影響します。", "（1.8）": "（1.8）", "柔": "柔", "硬": "硬", "張り": "張り", "材質の形状維持強度。ジョイントの強さ等に影響します。": "材質の形状維持強度。ジョイントの強さ等に影響します。", "（1.5）": "（1.5）", "弱": "弱", "強": "強", "（材質未選択）": "（材質未選択）", "ボーン密度": "ボーン密度", "縦密度": "縦密度", "ボーンの縦方向のメッシュに対する密度": "ボーンの縦方向のメッシュに対する密度", "横密度": "横密度", "ボーンの横方向のメッシュに対する密度": "ボーンの横方向のメッシュに対する密度", "物理タイプ": "物理タイプ", "布": "布", "末端剛体": "末端剛体", "末端剛体の質量": "末端剛体の質量", "移動減衰": "移動減衰", "末端剛体の移動減衰": "末端剛体の移動減衰", "回転減衰": "回転減衰", "末端剛体の回転減衰": "末端剛体の回転減衰", "反発力": "反発力", "末端剛体の反発力": "末端剛体の反発力", "摩擦力": "摩擦力", "末端剛体の摩擦力": "末端剛体の摩擦力", "係数": "係数", "末端剛体から上の剛体にかけての加算係数": "末端剛体から上の剛体にかけての加算係数", "剛体形状": "剛体形状", "剛体の形状": "剛体の形状", "球": "球", "箱": "箱", "カプセル": "カプセル", "縦ジョイント": "縦ジョイント", "有効": "有効", "縦ジョイントを有効にするか否か": "縦ジョイントを有効にするか否か", "制限係数": "制限係数", "根元ジョイントが末端ジョイントよりどれくらい制限を強くするか。1の場合、全ての段の制限が均一になります。": "根元ジョイントが末端ジョイントよりどれくらい制限を強くするか。1の場合、全ての段の制限が均一になります。", "移動X(最小)": "移動X(最小)", "末端縦ジョイントの移動X(最小)": "末端縦ジョイントの移動X(最小)", "移動Y(最小)": "移動Y(最小)", "末端縦ジョイントの移動Y(最小)": "末端縦ジョイントの移動Y(最小)", "移動Z(最小)": "移動Z(最小)", "末端縦ジョイントの移動Z(最小)": "末端縦ジョイントの移動Z(最小)", "移動X(最大)": "移動X(最大)", "末端縦ジョイントの移動X(最大)": "末端縦ジョイントの移動X(最大)", "移動Y(最大)": "移動Y(最大)", "末端縦ジョイントの移動Y(最大)": "末端縦ジョイントの移動Y(最大)", "移動Z(最大)": "移動Z(最大)", "末端縦ジョイントの移動Z(最大)": "末端縦ジョイントの移動Z(最大)", "回転X(最小)": "回転X(最小)", "末端縦ジョイントの回転X(最小)": "末端縦ジョイントの回転X(最小)", "回転Y(最小)": "回転Y(最小)", "末端縦ジョイントの回転Y(最小)": "末端縦ジョイントの回転Y(最小)", "回転Z(最小)": "回転Z(最小)", "末端縦ジョイントの回転Z(最小)": "末端縦ジョイントの回転Z(最小)", "回転X(最大)": "回転X(最大)", "末端縦ジョイントの回転X(最大)": "末端縦ジョイントの回転X(最大)", "回転Y(最大)": "回転Y(最大)", "末端縦ジョイントの回転Y(最大)": "末端縦ジョイントの回転Y(最大)", "回転Z(最大)": "回転Z(最大)", "末端縦ジョイントの回転Z(最大)": "末端縦ジョイントの回転Z(最大)", "ばね(移動X)": "ばね(移動X)", "末端縦ジョイントのばね(移動X)": "末端縦ジョイントのばね(移動X)", "ばね(移動Y)": "ばね(移動Y)", "末端縦ジョイントのばね(移動Y)": "末端縦ジョイントのばね(移動Y)", "ばね(移動Z)": "ばね(移動Z)", "末端縦ジョイントのばね(移動Z)": "末端縦ジョイントのばね(移動Z)", "ばね(回転X)": "ばね(回転X)", "末端縦ジョイントのばね(回転X)": "末端縦ジョイントのばね(回転X)", "ばね(回転Y)": "ばね(回転Y)", "末端縦ジョイントのばね(回転Y)": "末端縦ジョイントのばね(回転Y)", "ばね(回転Z)": "ばね(回転Z)", "末端縦ジョイントのばね(回転Z)": "末端縦ジョイントのばね(回転Z)", "横ジョイント": "横ジョイント", "横ジョイントを有効にするか否か": "横ジョイントを有効にするか否か", "末端横ジョイントの移動X(最小)": "末端横ジョイントの移動X(最小)", "末端横ジョイントの移動Y(最小)": "末端横ジョイントの移動Y(最小)", "末端横ジョイントの移動Z(最小)": "末端横ジョイントの移動Z(最小)", "末端横ジョイントの移動X(最大)": "末端横ジョイントの移動X(最大)", "末端横ジョイントの移動Y(最大)": "末端横ジョイントの移動Y(最大)", "末端横ジョイントの移動Z(最大)": "末端横ジョイントの移動Z(最大)", "末端横ジョイントの回転X(最小)": "末端横ジョイントの回転X(最小)", "末端横ジョイントの回転Y(最小)": "末端横ジョイントの回転Y(最小)", "末端横ジョイントの回転Z(最小)": "末端横ジョイントの回転Z(最小)", "末端横ジョイントの回転X(最大)": "末端横ジョイントの回転X(最大)", "末端横ジョイントの回転Y(最大)": "末端横ジョイントの回転Y(最大)", "末端横ジョイントの回転Z(最大)": "末端横ジョイントの回転Z(最大)", "末端横ジョイントのばね(移動X)": "末端横ジョイントのばね(移動X)", "末端横ジョイントのばね(移動Y)": "末端横ジョイントのばね(移動Y)", "末端横ジョイントのばね(移動Z)": "末端横ジョイントのばね(移動Z)", "末端横ジョイントのばね(回転X)": "末端横ジョイントのばね(回転X)", "末端横ジョイントのばね(回転Y)": "末端横ジョイントのばね(回転Y)", "末端横ジョイントのばね(回転Z)": "末端横ジョイントのばね(回転Z)", "斜めジョイント": "斜めジョイント", "斜めジョイントを有効にするか否か": "斜めジョイントを有効にするか否か", "末端斜めジョイントの移動X(最小)": "末端斜めジョイントの移動X(最小)", "末端斜めジョイントの移動Y(最小)": "末端斜めジョイントの移動Y(最小)", "末端斜めジョイントの移動Z(最小)": "末端斜めジョイントの移動Z(最小)", "末端斜めジョイントの移動X(最大)": "末端斜めジョイントの移動X(最大)", "末端斜めジョイントの移動Y(最大)": "末端斜めジョイントの移動Y(最大)", "末端斜めジョイントの移動Z(最大)": "末端斜めジョイントの移動Z(最大)", "末端斜めジョイントの回転X(最小)": "末端斜めジョイントの回転X(最小)", "末端斜めジョイントの回転Y(最小)": "末端斜めジョイントの回転Y(最小)", "末端斜めジョイントの回転Z(最小)": "末端斜めジョイントの回転Z(最小)", "末端斜めジョイントの回転X(最大)": "末端斜めジョイントの回転X(最大)", "末端斜めジョイントの回転Y(最大)": "末端斜めジョイントの回転Y(最大)", "末端斜めジョイントの回転Z(最大)": "末端斜めジョイントの回転Z(最大)", "末端斜めジョイントのばね(移動X)": "末端斜めジョイントのばね(移動X)", "末端斜めジョイントのばね(移動Y)": "末端斜めジョイントのばね(移動Y)", "末端斜めジョイントのばね(移動Z)": "末端斜めジョイントのばね(移動Z)", "末端斜めジョイントのばね(回転X)": "末端斜めジョイントのばね(回転X)", "末端斜めジョイントのばね(回転Y)": "末端斜めジョイントのばね(回転Y)", "末端斜めジョイントのばね(回転Z)": "末端斜めジョイントのばね(回転Z)", "逆ジョイント": "逆ジョイント", "逆ジョイントを有効にするか否か": "逆ジョイントを有効にするか否か", "末端逆ジョイントの移動X(最小)": "末端逆ジョイントの移動X(最小)", "末端逆ジョイントの移動Y(最小)": "末端逆ジョイントの移動Y(最小)", "末端逆ジョイントの移動Z(最小)": "末端逆ジョイントの移動Z(最小)", "末端逆ジョイントの移動X(最大)": "末端逆ジョイントの移動X(最大)", "末端逆ジョイントの移動Y(最大)": "末端逆ジョイントの移動Y(最大)", "末端逆ジョイントの移動Z(最大)": "末端逆ジョイントの移動Z(最大)", "末端逆ジョイントの回転X(最小)": "末端逆ジョイントの回転X(最小)", "末端逆ジョイントの回転Y(最小)": "末端逆ジョイントの回転Y(最小)", "末端逆ジョイントの回転Z(最小)": "末端逆ジョイントの回転Z(最小)", "末端逆ジョイントの回転X(最大)": "末端逆ジョイントの回転X(最大)", "末端逆ジョイントの回転Y(最大)": "末端逆ジョイントの回転Y(最大)", "末端逆ジョイントの回転Z(最大)": "末端逆ジョイントの回転Z(最大)", "末端逆ジョイントのばね(移動X)": "末端逆ジョイントのばね(移動X)", "末端逆ジョイントのばね(移動Y)": "末端逆ジョイントのばね(移動Y)", "末端逆ジョイントのばね(移動Z)": "末端逆ジョイントのばね(移動Z)", "末端逆ジョイントのばね(回転X)": "末端逆ジョイントのばね(回転X)", "末端逆ジョイントのばね(回転Y)": "末端逆ジョイントのばね(回転Y)", "末端逆ジョイントのばね(回転Z)": "末端逆ジョイントのばね(回転Z)", "ボーン設定データをcsvファイルから読み込みます。\nファイル選択ダイアログが開きます。": "ボーン設定データをcsvファイルから読み込みます。\nファイル選択ダイアログが開きます。", "ボーン設定データをcsvファイルに出力します。\n出力先を指定できます。": "ボーン設定データをcsvファイルに出力します。\n出力先を指定できます。", "パラ調整(詳細)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "パラ調整(詳細)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....", "PmxTailor変換処理実行": "PmxTailor変換処理実行", "元モデル": "元モデル", "材質": "材質", "剛体グループ": "剛体グループ", "【%s】頂点マップ生成": "【%s】頂点マップ生成", "%s: 面の抽出": "%s: 面の抽出", "%s: 面の抽出準備①": "%s: 面の抽出準備①", "%s: 面の抽出準備②": "%s: 面の抽出準備②", "%s: 相対頂点マップの生成": "%s: 相対頂点マップの生成", "-- 面: %s個目:終了": "-- 面: %s個目:終了", "%s: 絶対頂点マップの生成": "%s: 絶対頂点マップの生成", "-- 絶対頂点マップ: %s個目: ---------": "-- 絶対頂点マップ: %s個目: ---------", "-- 絶対頂点マップ: %s個目:終了 ---------": "-- 絶対頂点マップ: %s個目:終了 ---------", "【%s】ボーン生成": "【%s】ボーン生成", "【%s(No.%s)】ウェイト分布": "【%s(No.%s)】ウェイト分布", "-- 頂点ウェイト: %s個目:終了": "-- 頂点ウェイト: %s個目:終了", "【%s(No.%s)】剛体生成": "【%s(No.%s)】剛体生成", "-- 剛体: %s個目:終了": "-- 剛体: %s個目:終了", "【%s(No.%s)】ジョイント生成": "【%s(No.%s)】ジョイント生成", "-- ジョイント: %s個目:終了": "-- ジョイント: %s個目:終了", "PMX出力開始": "PMX出力開始", "-- 頂点データ出力終了(%s)": "-- 頂点データ出力終了(%s)", "-- 面データ出力終了(%s)": "-- 面データ出力終了(%s)", "-- テクスチャデータ出力終了(%s)": "-- テクスチャデータ出力終了(%s)", "-- 材質データ出力終了(%s)": "-- 材質データ出力終了(%s)", "-- ボーンデータ出力終了(%s)": "-- ボーンデータ出力終了(%s)", "-- モーフデータ出力終了(%s)": "-- モーフデータ出力終了(%s)", "-- 表示枠データ出力終了(%s)": "-- 表示枠データ出力終了(%s)", "-- 剛体データ出力終了(%s)": "-- 剛体データ出力終了(%s)", "-- ジョイントデータ出力終了(%s)": "-- ジョイントデータ出力終了(%s)", "出力終了: %s": "出力終了: %s", "PmxTailor変換処理が意図せぬエラーで終了しました。\n\n%s": "PmxTailor変換処理が意図せぬエラーで終了しました。\n\n%s", "\n処理時間: {0}": "\n処理時間: {0}", "\n処理時間: 05s": "\n処理時間: 05s", "既存物理を再利用する場合、「パラ調整(ボーン)」画面でボーン並び順を指定してください。": "既存物理を再利用する場合、「パラ調整(ボーン)」画面でボーン並び順を指定してください。", "有効な物理設定が1件も設定されていません。\nモデルを選択しなおした場合、物理設定は初期化されます。": "有効な物理設定が1件も設定されていません。\nモデルを選択しなおした場合、物理設定は初期化されます。", "【%s】残ウェイト分布": "【%s】残ウェイト分布", "-- 残頂点ウェイト: %s個目:終了": "-- 残頂点ウェイト: %s個目:終了", "\n処理時間: 04s": "\n処理時間: 04s", "\n処理時間: 08s": "\n処理時間: 08s", "ボーン設定CSVを読み込む": "ボーン設定CSVを読み込む", "ボーン設定CSVのインポートに成功しました \n{0}": "ボーン設定CSVのインポートに成功しました \n{0}", "ボーン設定CSVのインポートに成功しました \n%s": "ボーン設定CSVのインポートに成功しました \n%s", "パラ調整(ボーン)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....": "パラ調整(ボーン)タブ表示準備開始\nファイル読み込み処理を実行します。少しお待ちください....", "1番目の対象モデルが見つかりませんでした。\n入力パス: %s": "1番目の対象モデルが見つかりませんでした。\n入力パス: %s", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。": "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。", "\n既に指定済みの場合、現在読み込み中の可能性があります。": "\n既に指定済みの場合、現在読み込み中の可能性があります。", "\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。": "「ファイル」タブで対象モデルファイルパスが指定されていないため、「{0}」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「{0}」タブを開いてください。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「%s」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「%s」タブを開いてください。": "「ファイル」タブで対象モデルファイルパスが指定されていないため、「%s」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「%s」タブを開いてください。", "「ファイル」タブで対象モデルファイルパスが指定されていないため、「パラ調整」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「パラ調整」タブを開いてください。": "「ファイル」タブで対象モデルファイルパスが指定されていないため、「パラ調整」タブが開けません。\n既に指定済みの場合、現在読み込み中の可能性があります。\n「■読み込み成功」のログが出てから、「パラ調整」タブを開いてください。", "\n処理時間: %s": "\n処理時間: %s", "ボーン設定CSVを保存する": "ボーン設定CSVを保存する", "ボーン設定CSVのエクスポートに成功しました \n{0}": "ボーン設定CSVのエクスポートに成功しました \n{0}", "「パラ調整(ボーン)」画面でボーン並び順を指定した場合、既存物理は「再利用」を指定してください。": "「パラ調整(ボーン)」画面でボーン並び順を指定した場合、既存物理は「再利用」を指定してください。", "【%s】既存材質削除": "【%s】既存材質削除", "%s: 削除対象抽出": "%s: 削除対象抽出", "%s: 削除実行": "%s: 削除実行", "%s: INDEX振り直し": "%s: INDEX振り直し", "%s: INDEX再割り当て": "%s: INDEX再割り当て", "【%s】ボーンマップ生成": "【%s】ボーンマップ生成", "【%s】剛体生成": "【%s】剛体生成", "【%s】ジョイント生成": "【%s】ジョイント生成", "履歴": "履歴", "これまで指定された対象モデルを再指定できます。": "これまで指定された対象モデルを再指定できます。", "開く": "開く", "ファイルを選んでダブルクリック、またはOKボタンをクリックしてください。": "ファイルを選んでダブルクリック、またはOKボタンをクリックしてください。", "{0}番目の": "{0}番目の", "【%s】裏面ウェイト分布": "【%s】裏面ウェイト分布", "-- 裏頂点ウェイト: %s個目:終了": "-- 裏頂点ウェイト: %s個目:終了", "成功": "成功", "髪": "髪", "物理": "物理", "Vroid2Pmx ローカル版": "Vroid2Pmx ローカル版", "Vroid2Pmx実行": "Vroid2Pmx実行", "Vroid2Pmx停止": "Vroid2Pmx停止", "Vrmモデルの指定された材質に物理を設定します。\n": "Vrmモデルの指定された材質に物理を設定します。\n", "Vrmモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。": "Vrmモデルを読み込んだ後、パラ調整タブで物理の設定を行ってください。", "対象モデルVrmファイルを開く": "対象モデルVrmファイルを開く", "変換したいVrmファイルパスを指定してください\nVroid Studio 正式版(1.0.0)以降のみ対応しています。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "変換したいVrmファイルパスを指定してください\nVroid Studio 正式版(1.0.0)以降のみ対応しています。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。", "Vrmモデルに物理を設定します": "Vrmモデルに物理を設定します", "-- JSON出力終了": "-- JSON出力終了", "-- テクスチャデータ解析終了": "-- テクスチャデータ解析終了", "Vroid2Pmx実行処理を中断します。": "Vroid2Pmx実行処理を中断します。", "-- -- Accessor[%s/%s/%s]": "-- -- Accessor[%s/%s/%s]", "-- -- Accessor[%s/%s/%s][%s]": "-- -- Accessor[%s/%s/%s][%s]", "物理を設定する材質を選択してください。\n裾など一部にのみ物理を設定したい場合、頂点データCSVを指定してください。": "物理を設定する材質を選択してください。\n裾など一部にのみ物理を設定したい場合、頂点データCSVを指定してください。", "対象頂点CSV": "対象頂点CSV", "対象頂点CSVファイルを開く": "対象頂点CSVファイルを開く", "材質の中で物理を割り当てたい頂点を絞り込みたい場合、PmxEditorで頂点リストを選択できるようにして保存した頂点CSVファイルを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。": "材質の中で物理を割り当てたい頂点を絞り込みたい場合、PmxEditorで頂点リストを選択できるようにして保存した頂点CSVファイルを指定してください。\nD&Dでの指定、開くボタンからの指定、履歴からの選択ができます。", "これまで指定された対象頂点CSVを再指定できます。": "これまで指定された対象頂点CSVを再指定できます。", "%s: 面の抽出準備③": "%s: 面の抽出準備③", "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質の最も近い頂点ウェイトを転写します": "物理材質の裏面にあたる材質がある場合、選択してください。\n物理材質の最も近い頂点ウェイトを転写します", "裾材質": "裾材質", "物理材質の裾にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます": "物理材質の裾にあたる材質がある場合、選択してください。\n物理材質のボーン割りに応じてウェイトを割り当てます", "【%s】裾ウェイト分布": "【%s】裾ウェイト分布", "Pmxモデルに物理を設定します": "Pmxモデルに物理を設定します", "材質を選択して、パラメーターを調整してください。\nスライダーパラメーターで調整した設定に基づいて詳細タブ内のMMD物理パラメーターを変更します。\n物理を再利用したい場合は、ボーンパネルでボーンの並び順を指定してください。": "材質を選択して、パラメーターを調整してください。\nスライダーパラメーターで調整した設定に基づいて詳細タブ内のMMD物理パラメーターを変更します。\n物理を再利用したい場合は、ボーンパネルでボーンの並び順を指定してください。", "親ボーン *　": "親ボーン *　", "裾材質　　": "裾材質　　", "裏面材質　": "裏面材質　", "VrmモデルをPmxモデルに変換します。\n": "VrmモデルをPmxモデルに変換します。\n", "物理を変えたい場合は、変換後のPmxデータをPmxTailorにかけてください。": "物理を変えたい場合は、変換後のPmxデータをPmxTailorにかけてください。", "VrmモデルをPmxモデルに変換します": "VrmモデルをPmxモデルに変換します", "-- 頂点データ解析[%s]": "-- 頂点データ解析[%s]", "-- 面・材質データ解析[%s-%s]": "-- 面・材質データ解析[%s-%s]", "-- ボーンデータ解析終了": "-- ボーンデータ解析終了", "-- 頂点・面・材質データ解析終了": "-- 頂点・面・材質データ解析終了", "-- ボーンデータ調整終了": "-- ボーンデータ調整終了", "設定クリア": "設定クリア", "ボーン設定データ全てクリアします。": "ボーン設定データ全てクリアします。", "（デバッグ版）": "（デバッグ版）", "-- Aスタンス調整終了": "-- Aスタンス調整終了", "-- グループモーフデータ解析": "-- グループモーフデータ解析", "-- 身体剛体設定終了": "-- 身体剛体設定終了", "exeバージョン": "exeバージョン", "物理設定クリア": "物理設定クリア", "有効な頂点マップが生成できなかった為、処理を終了します": "有効な頂点マップが生成できなかった為、処理を終了します", "Vroid2Pmx処理実行": "Vroid2Pmx処理実行", "PMX出力": "PMX出力", "作者": "作者", "連絡先": "連絡先", "参照": "参照", "バージョン": "バージョン", "アバターの人格に関する許諾範囲": "アバターの人格に関する許諾範囲", "アバターに人格を与えることの許諾範囲": "アバターに人格を与えることの許諾範囲", "このアバターを用いて暴力表現を演じることの許可": "このアバターを用いて暴力表現を演じることの許可", "このアバターを用いて性的表現を演じることの許可": "このアバターを用いて性的表現を演じることの許可", "商用利用の許可": "商用利用の許可", "その他のライセンス条件": "その他のライセンス条件", "再配布・改変に関する許諾範囲": "再配布・改変に関する許諾範囲", "ライセンスタイプ": "ライセンスタイプ", "物理を設定したい場合は、変換後のPmxデータをPmxTailorにかけてください。": "物理を設定したい場合は、変換後のPmxデータをPmxTailorにかけてください。", "アバター情報": "アバター情報", "出力ソフト情報がないため、処理を中断します。": "出力ソフト情報がないため、処理を中断します。", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。": "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。", "メタ情報がないため、処理を中断します。": "メタ情報がないため、処理を中断します。", "Vroid Studio 正式版(1.0.0)以降でエクスポートされたVrmモデルをPmxモデルに変換します。\n": "Vroid Studio 正式版(1.0.0)以降でエクスポートされたVrmモデルをPmxモデルに変換します。\n", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .pmx\n設定可能拡張子: vrm": "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .pmx\n設定可能拡張子: vrm", "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。\n出力元: %s": "VRoid Studio 1.0.x で出力されたvrmデータではないため、処理を中断します。\n出力元: %s", "VRoid Studio ベータ版 で出力されたvrmデータではあるため、処理を中断します。\n正式版でコンバートしてから再度試してください。\n出力元: %s": "VRoid Studio ベータ版 で出力されたvrmデータではあるため、処理を中断します。\n正式版でコンバートしてから再度試してください。\n出力元: %s", "VRoid Studio 1.x で出力されたvrmデータではないため、結果がおかしくなる可能性があります。\n（結果がおかしくてもサポート対象外となります）\n出力元: %s": "VRoid Studio 1.x で出力されたvrmデータではないため、結果がおかしくなる可能性があります。\n（結果がおかしくてもサポート対象外となります）\n出力元: %s", "val_type in [TYPE_INT, TYPE_UNSIGNED_INT]: %s": "val_type in [TYPE_INT, TYPE_UNSIGNED_INT]: %s", "write_number失敗: type: %s, val: %s, int(val): %s": "write_number失敗: type: %s, val: %s, int(val): %s", "髪の反射色がVrmデータになかったため、仮に白色を差し込みます": "髪の反射色がVrmデータになかったため、仮に白色を差し込みます", "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s": "Vroid2Pmx処理が意図せぬエラーで終了しました。\n\n%s", "物理(簡易)を入れる": "物理(簡易)を入れる", "チェックを入れると、物理(簡易)を設定します。\nVRoid Studio で設定された物理をそのまま再現は出来てません。\nPmxTailor で入れた物理に比べて固くなりがちです。": "チェックを入れると、物理(簡易)を設定します。\nVRoid Studio で設定された物理をそのまま再現は出来てません。\nPmxTailor で入れた物理に比べて固くなりがちです。", "-- -- PmxTailor用設定ファイル出力準備終了": "-- -- PmxTailor用設定ファイル出力準備終了", "胸(小)": "胸(小)", "髪(ロング)": "髪(ロング)", "-- PmxTailor用設定ファイル出力終了": "-- PmxTailor用設定ファイル出力終了", "髪(ショート)": "髪(ショート)", "-- -- PmxTailor用設定ファイル出力準備 (%s)": "-- -- PmxTailor用設定ファイル出力準備 (%s)", "単一揺れ物": "単一揺れ物", "VRoid Studioで物理が設定ボーンの変換先が見つかりませんでした。 ボーン名: %s": "VRoid Studioで物理が設定ボーンの変換先が見つかりませんでした。 ボーン名: %s", "既に同じ材質名が登録されているため、元の材質名のまま登録します 変換材質名: %s 元材質名: %s": "既に同じ材質名が登録されているため、元の材質名のまま登録します 変換材質名: %s 元材質名: %s", "-- -- PmxTailor用設定ファイル出力 (%s)": "-- -- PmxTailor用設定ファイル出力 (%s)", "髪(アホ毛)": "髪(アホ毛)", "胸(大)": "胸(大)", "-- -- モーフ調整[%s]": "-- -- モーフ調整[%s]", "-- -- モーフ調整: %s個目": "-- -- モーフ調整: %s個目", "-- -- 拡張モーフ調整: %s個目": "-- -- 拡張モーフ調整: %s個目", "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .vroid\n設定可能拡張子: vrm": "対象モデルの拡張子が正しくありません。\n入力ファイル拡張子: .vroid\n設定可能拡張子: vrm", "基本色が白ではないため、加算します。": "基本色が白ではないため、加算します。", "基本色が白ではないため、加算合成します。 材質名: %s": "基本色が白ではないため、加算合成します。 材質名: %s", "ダブルクリックされました。": "ダブルクリックされました。", "まだ処理が実行中です。終了してから再度実行してください。": "まだ処理が実行中です。終了してから再度実行してください。", "テクスチャの透明部分がUVに含まれているため、エッジ材質を作成します 材質名: %s": "テクスチャの透明部分がUVに含まれているため、エッジ材質を作成します 材質名: %s", "-- -- モーフ調整準備": "-- -- モーフ調整準備", "Fcl_EYE_Surprised モーフがなかったため、瞳小モーフ生成をスルーします": "Fcl_EYE_Surprised モーフがなかったため、瞳小モーフ生成をスルーします", "Fcl_EYE_Surprised モーフがなかったため、瞳大モーフ生成をスルーします": "Fcl_EYE_Surprised モーフがなかったため、瞳大モーフ生成をスルーします", "Fcl_EYE_Close モーフがなかったため、目隠し頂点モーフ生成をスルーします": "Fcl_EYE_Close モーフがなかったため、目隠し頂点モーフ生成をスルーします", "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s": "VRoid Studioで設定された物理をPmxTailor用設定に変換できませんでした。 定義名: %s, 材質名: %s, ボーン名: %s", "布(ベルベット)": "布(ベルベット)", "-- PmxTailor用設定ファイル出力準備1": "-- PmxTailor用設定ファイル出力準備1", "-- -- PmxTailor用設定ファイル出力準備1 (%s)": "-- -- PmxTailor用設定ファイル出力準備1 (%s)", "-- PmxTailor用設定ファイル出力準備2": "-- PmxTailor用設定ファイル出力準備2", "-- -- PmxTailor用設定ファイル出力準備2 (%s)": "-- -- PmxTailor用設定ファイル出力準備2 (%s)", "-- 身体剛体準備終了": "-- 身体剛体準備終了", "-- -- 身体剛体[%s]": "-- -- 身体剛体[%s]", "舌関連頂点が見つからなかったため、舌分離処理をスキップします": "舌関連頂点が見つからなかったため、舌分離処理をスキップします", "-- Aスタンス・親指調整終了": "-- Aスタンス・親指調整終了", "出力ソフト情報がないため、処理を中断します。\nvrm1.0でエクスポートした場合、vrm0.0でエクスポートし直してください。": "出力ソフト情報がないため、処理を中断します。\nvrm1.0でエクスポートした場合、vrm0.0でエクスポートし直してください。"}