# -*- coding: utf-8 -*-
#
import logging
import traceback
import os
from pathlib import Path

from module.MOptions import MExportOptions
from mmd.PmxData import PmxModel
from mmd.PmxWriter import PmxWriter
from utils import MServiceUtils, MFileUtils
from utils.MLogger import <PERSON><PERSON><PERSON><PERSON>
from utils.MException import <PERSON>zingException, MKilledException

# 导入拆分后的模块
from .bone_processor import BoneProcessor
from .mesh_processor import MeshProcessor
from .morph_processor import MorphProcessor
from .stance_processor import StanceProcessor
from .rigidbody_processor import RigidbodyProcessor
from .pmxtailor_exporter import PmxTailorExporter
from .model_creator import ModelCreator

logger = MLogger(__name__, level=1)


class VroidExportService:
    def __init__(self, options: MExportOptions):
        self.options = options

        # 初始化处理器
        self.bone_processor = BoneProcessor()
        self.mesh_processor = MeshProcessor()
        self.morph_processor = MorphProcessor()
        self.stance_processor = StanceProcessor()
        self.rigidbody_processor = RigidbodyProcessor()
        self.pmxtailor_exporter = PmxTailorExporter()
        self.model_creator = ModelCreator(options)

    def execute(self):
        logging.basicConfig(level=self.options.logging_level, format="%(message)s [%(module_name)s]")

        try:
            service_data_txt = f"{logger.transtext('Vroid2Pmx処理実行')}\n------------------------\n{logger.transtext('exeバージョン')}: {self.options.version_name}\n"
            service_data_txt = (
                f"{service_data_txt}　{logger.transtext('元モデル')}: {os.path.basename(self.options.vrm_model.path)}\n"
            )

            logger.info(service_data_txt, translate=False, decoration=MLogger.DECORATION_BOX)

            model = self.vroid2pmx()
            if not model:
                return False

            # PMXファイル出力
            pmx_writer = PmxWriter()
            pmx_writer.write(model, self.options.output_path)

            logger.info("出力終了", decoration=MLogger.DECORATION_BOX)
            return True

        except MKilledException as ke:
            logger.error("処理が中断されました。", decoration=MLogger.DECORATION_BOX)
            return False
        except SizingException as se:
            logger.error("サイジング処理が失敗しました。\n\n%s", se.message, decoration=MLogger.DECORATION_BOX)
            return False
        except Exception as e:
            logger.critical("予期しないエラーが発生しました。", e, decoration=MLogger.DECORATION_BOX)
            return False
        finally:
            logging.shutdown()

    def vroid2pmx(self):
        try:
            model, tex_dir_path, glft_dir_path, setting_dir_path = self.model_creator.create_model()
            if not model:
                return False

            # 检测VRoid版本
            is_vroid1 = self._detect_vroid_version(model)

            model, bone_name_dict = self.bone_processor.convert_bone(model)
            if not model:
                return False

            model = self.mesh_processor.convert_mesh(model, bone_name_dict, tex_dir_path)
            if not model:
                return False

            model = self.bone_processor.reconvert_bone(model)
            if not model:
                return False

            model = self.morph_processor.convert_morph(model, is_vroid1)
            if not model:
                return False

            model = self.stance_processor.transfer_stance(model)
            if not model:
                return False

            model = self.rigidbody_processor.create_body_rigidbody(model)
            if not model:
                return False

            self.pmxtailor_exporter.export_pmxtailor_setting(model, setting_dir_path)

            return model

        except Exception as e:
            logger.error("VRoid to PMX conversion failed", e, decoration=MLogger.DECORATION_BOX)
            raise e

    def _detect_vroid_version(self, model):
        """检测VRoid版本"""
        # 简单的版本检测逻辑
        if "Fcl_EYE_Close" in getattr(model, 'org_morphs', {}):
            return True  # VRoid 1.x
        return False  # VRoid 0.x
