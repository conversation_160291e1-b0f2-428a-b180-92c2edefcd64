# -*- coding: utf-8 -*-
"""
网格处理模块
"""

import os
import struct
import numpy as np
from pathlib import Path
from module.MMath import MVector2D, MVector3D, MVector4D, MQuaternion
from mmd.PmxData import PmxModel, Vertex, Material, Bdef1, Bdef2, Bdef4
from utils.MLogger import <PERSON><PERSON><PERSON><PERSON>
from .vroid_utils import read_from_accessor, get_deform_index
from .vroid_constants import MIME_TYPE, MIKU_METER

logger = MLogger(__name__)


class MeshProcessor:
    """网格处理器"""
    
    def __init__(self):
        pass
    
    def convert_mesh(self, model: PmxModel, bone_name_dict: dict, tex_dir_path: str):
        """转换网格"""
        if "meshes" not in model.json_data:
            logger.error("変換可能なメッシュ情報がないため、処理を中断します。", decoration=MLogger.DECORATION_BOX)
            return None

        vertex_blocks = {}
        vertex_idx = 0

        for midx, mesh in enumerate(model.json_data["meshes"]):
            if "primitives" not in mesh:
                continue

            for pidx, primitive in enumerate(mesh["primitives"]):
                if (
                    "attributes" not in primitive
                    or "indices" not in primitive
                    or "material" not in primitive
                    or "JOINTS_0" not in primitive["attributes"]
                    or "NORMAL" not in primitive["attributes"]
                    or "POSITION" not in primitive["attributes"]
                    or "TEXCOORD_0" not in primitive["attributes"]
                    or "WEIGHTS_0" not in primitive["attributes"]
                ):
                    continue

                logger.info(f"メッシュ変換: {midx}-{pidx}")

                # 頂点位置
                position_values = read_from_accessor(model, primitive["attributes"]["POSITION"])
                # 法線
                normal_values = read_from_accessor(model, primitive["attributes"]["NORMAL"])
                # UV
                uv_values = read_from_accessor(model, primitive["attributes"]["TEXCOORD_0"])
                # ジョイント
                joint_values = read_from_accessor(model, primitive["attributes"]["JOINTS_0"])
                # ウェイト
                weight_values = read_from_accessor(model, primitive["attributes"]["WEIGHTS_0"])
                # インデックス
                indices_values = read_from_accessor(model, primitive["indices"])

                # スキン情報
                skin_joints = []
                if "skins" in model.json_data and len(model.json_data["skins"]) > 0:
                    skin_joints = model.json_data["skins"][0]["joints"]

                # 頂点データ変換
                vertices = []
                for vidx in range(len(position_values)):
                    # 位置 - read_from_accessor已经返回MVector3D对象
                    pos_vec = position_values[vidx]
                    position = MVector3D(pos_vec.x(), pos_vec.y(), -pos_vec.z()) * MIKU_METER

                    # 法線 - read_from_accessor已经返回MVector3D对象
                    normal_vec = normal_values[vidx]
                    normal = MVector3D(normal_vec.x(), normal_vec.y(), -normal_vec.z())

                    # UV - read_from_accessor已经返回MVector2D对象
                    uv_vec = uv_values[vidx]
                    uv = MVector2D(uv_vec.x(), 1 - uv_vec.y())

                    # ジョイントとウェイト - read_from_accessor已经返回MVector4D对象
                    joint = joint_values[vidx]
                    weight = weight_values[vidx]

                    # 変形情報を取得 - get_deform_index直接返回deform对象
                    deform = get_deform_index(
                        vidx, model, position, joint, skin_joints, [weight], bone_name_dict
                    )

                    # デフォルト値設定
                    if deform is None:
                        deform = Bdef1(0)

                    vertex = Vertex(
                        vertex_idx + vidx,  # index
                        position,           # position
                        normal,             # normal
                        uv,                 # uv
                        [],                 # extended_uvs
                        deform,             # deform
                        1.0                 # edge_factor
                    )
                    vertices.append(vertex)

                # 面データ変換 - read_from_accessor已经返回整数列表
                faces = []
                for i in range(0, len(indices_values), 3):
                    idx_data0 = indices_values[i]
                    idx_data1 = indices_values[i + 1]
                    idx_data2 = indices_values[i + 2]

                    # 面の向きを反転（右手座標系から左手座標系へ）
                    face_indices = [
                        vertex_idx + idx_data0,
                        vertex_idx + idx_data2,
                        vertex_idx + idx_data1
                    ]
                    faces.extend(face_indices)

                # マテリアル処理
                material_idx = primitive["material"]
                material_name = f"Material_{material_idx}"
                
                if material_idx < len(model.json_data["materials"]):
                    material_data = model.json_data["materials"][material_idx]
                    if "name" in material_data:
                        material_name = material_data["name"]

                # マテリアルが存在しない場合は作成
                if material_name not in model.materials:
                    material = Material(
                        material_name,                          # name
                        material_name,                          # english_name
                        MVector4D(1, 1, 1, 1),                 # diffuse_color
                        1.0,                                    # alpha
                        0.0,                                    # specular_factor
                        MVector3D(0, 0, 0),                    # specular_color
                        MVector3D(0.2, 0.2, 0.2),             # ambient_color
                        0x01 | 0x02 | 0x04 | 0x08 | 0x10,     # flag
                        MVector4D(0, 0, 0, 1),                 # edge_color
                        1.0,                                    # edge_size
                        -1,                                     # texture_index
                        -1,                                     # sphere_texture_index
                        0,                                      # sphere_mode
                        0                                       # toon_sharing_flag
                    )
                    
                    # テクスチャ処理
                    if (material_idx < len(model.json_data["materials"]) and 
                        "pbrMetallicRoughness" in model.json_data["materials"][material_idx] and
                        "baseColorTexture" in model.json_data["materials"][material_idx]["pbrMetallicRoughness"]):
                        
                        texture_info = model.json_data["materials"][material_idx]["pbrMetallicRoughness"]["baseColorTexture"]
                        texture_idx = texture_info["index"]
                        
                        if texture_idx < len(model.json_data["textures"]):
                            texture_data = model.json_data["textures"][texture_idx]
                            image_idx = texture_data["source"]
                            
                            if image_idx < len(model.json_data["images"]):
                                image_data = model.json_data["images"][image_idx]
                                
                                # テクスチャファイルを保存
                                texture_name = self._save_texture(model, image_data, tex_dir_path)
                                if texture_name:
                                    texture_path = os.path.join("tex", texture_name)
                                    if texture_path not in model.textures:
                                        model.textures.append(texture_path)
                                    material.texture_index = model.textures.index(texture_path)
                    
                    model.materials[material_name] = material

                # 頂点とフェイスをモデルに追加
                vertex_index_map = {}  # 原始索引到新索引的映射
                for i, vertex in enumerate(vertices):
                    old_index = vertex.index
                    new_index = len(model.vertex_dict)
                    vertex.index = new_index
                    vertex_index_map[vertex_idx + i] = new_index
                    model.vertex_dict[new_index] = vertex

                    # 頂点をウェイトボーンごとに分けて保持する（PmxReaderと同じ構造）
                    for bone_idx in vertex.deform.get_idx_list():
                        if bone_idx not in model.vertices:
                            model.vertices[bone_idx] = []
                        model.vertices[bone_idx].append(vertex)

                # 面データをモデルに追加（索引を新しい頂点索引に更新）
                face_start_idx = len(model.indices)
                for i in range(0, len(faces), 3):
                    # 面索引を新しい頂点索引に変換
                    new_face = [
                        vertex_index_map.get(faces[i], faces[i]),
                        vertex_index_map.get(faces[i + 1], faces[i + 1]),
                        vertex_index_map.get(faces[i + 2], faces[i + 2])
                    ]
                    model.indices[face_start_idx + i // 3] = new_face

                vertex_idx += len(vertices)

        return model

    def _save_texture(self, model, image_data, tex_dir_path):
        """テクスチャファイルを保存"""
        try:
            if "uri" in image_data:
                # データURIの場合
                uri = image_data["uri"]
                if uri.startswith("data:"):
                    import base64
                    header, data = uri.split(",", 1)
                    mime_type = header.split(";")[0].split(":")[1]
                    
                    if mime_type in MIME_TYPE:
                        ext = MIME_TYPE[mime_type]
                        texture_name = f"texture_{len(model.textures)}.{ext}"
                        texture_path = os.path.join(tex_dir_path, texture_name)
                        
                        with open(texture_path, "wb") as f:
                            f.write(base64.b64decode(data))
                        
                        return texture_name
            elif "bufferView" in image_data:
                # バッファビューの場合
                buffer_view_idx = image_data["bufferView"]
                if buffer_view_idx < len(model.json_data["bufferViews"]):
                    buffer_view = model.json_data["bufferViews"][buffer_view_idx]
                    buffer_idx = buffer_view["buffer"]
                    
                    if buffer_idx in model.buffer_data:
                        buffer_data = model.buffer_data[buffer_idx]
                        start = buffer_view["byteOffset"]
                        length = buffer_view["byteLength"]
                        
                        mime_type = image_data.get("mimeType", "image/png")
                        if mime_type in MIME_TYPE:
                            ext = MIME_TYPE[mime_type]
                            texture_name = f"texture_{len(model.textures)}.{ext}"
                            texture_path = os.path.join(tex_dir_path, texture_name)
                            
                            with open(texture_path, "wb") as f:
                                f.write(buffer_data[start:start + length])
                            
                            return texture_name
        except Exception as e:
            logger.warning(f"テクスチャ保存エラー: {e}")
        
        return None
