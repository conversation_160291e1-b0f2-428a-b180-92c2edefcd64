# -*- coding: utf-8 -*-
"""
网格处理模块
"""

import os
import struct
import numpy as np
from pathlib import Path
from module.MMath import MVector3D, MVector4D, MQuaternion
from mmd.PmxData import PmxModel, Vertex, Material, Bdef1, Bdef2, Bdef4
from utils.MLogger import <PERSON><PERSON>ogger
from .vroid_utils import read_from_accessor, get_deform_index
from .vroid_constants import MIME_TYPE, MIKU_METER

logger = MLogger(__name__)


class MeshProcessor:
    """网格处理器"""
    
    def __init__(self):
        pass
    
    def convert_mesh(self, model: PmxModel, bone_name_dict: dict, tex_dir_path: str):
        """转换网格"""
        if "meshes" not in model.json_data:
            logger.error("変換可能なメッシュ情報がないため、処理を中断します。", decoration=MLogger.DECORATION_BOX)
            return None

        vertex_blocks = {}
        vertex_idx = 0

        for midx, mesh in enumerate(model.json_data["meshes"]):
            if "primitives" not in mesh:
                continue

            for pidx, primitive in enumerate(mesh["primitives"]):
                if (
                    "attributes" not in primitive
                    or "indices" not in primitive
                    or "material" not in primitive
                    or "JOINTS_0" not in primitive["attributes"]
                    or "NORMAL" not in primitive["attributes"]
                    or "POSITION" not in primitive["attributes"]
                    or "TEXCOORD_0" not in primitive["attributes"]
                    or "WEIGHTS_0" not in primitive["attributes"]
                ):
                    continue

                logger.info(f"メッシュ変換: {midx}-{pidx}")

                # 頂点位置
                position_values = read_from_accessor(model, primitive["attributes"]["POSITION"])
                # 法線
                normal_values = read_from_accessor(model, primitive["attributes"]["NORMAL"])
                # UV
                uv_values = read_from_accessor(model, primitive["attributes"]["TEXCOORD_0"])
                # ジョイント
                joint_values = read_from_accessor(model, primitive["attributes"]["JOINTS_0"])
                # ウェイト
                weight_values = read_from_accessor(model, primitive["attributes"]["WEIGHTS_0"])
                # インデックス
                indices_values = read_from_accessor(model, primitive["indices"])

                # スキン情報
                skin_joints = []
                if "skins" in model.json_data and len(model.json_data["skins"]) > 0:
                    skin_joints = model.json_data["skins"][0]["joints"]

                # 頂点データ変換
                vertices = []
                for vidx in range(len(position_values)):
                    # 位置
                    pos_data = struct.unpack("<fff", position_values[vidx])
                    position = MVector3D(pos_data[0], pos_data[1], -pos_data[2]) * MIKU_METER

                    # 法線
                    normal_data = struct.unpack("<fff", normal_values[vidx])
                    normal = MVector3D(normal_data[0], normal_data[1], -normal_data[2])

                    # UV
                    uv_data = struct.unpack("<ff", uv_values[vidx])
                    uv = MVector3D(uv_data[0], 1 - uv_data[1], 0)

                    # ジョイントとウェイト
                    joint_data = struct.unpack("<HHHH", joint_values[vidx])
                    weight_data = struct.unpack("<ffff", weight_values[vidx])
                    
                    joint = MVector4D(*joint_data)
                    weight = MVector4D(*weight_data)

                    # 変形情報を取得
                    deform_info = get_deform_index(
                        vidx, model, position, joint, skin_joints, [weight], bone_name_dict
                    )

                    # 変形タイプを決定
                    deform = None
                    if deform_info and deform_info['weights']:
                        weights = deform_info['weights']
                        bone_names = deform_info['bone_names']
                        
                        if len(weights) == 1:
                            # BDEF1
                            bone_idx = list(model.bones.keys()).index(bone_names[0]) if bone_names[0] in model.bones else 0
                            deform = Bdef1(bone_idx)
                        elif len(weights) == 2:
                            # BDEF2
                            bone_idx0 = list(model.bones.keys()).index(bone_names[0]) if bone_names[0] in model.bones else 0
                            bone_idx1 = list(model.bones.keys()).index(bone_names[1]) if bone_names[1] in model.bones else 0
                            deform = Bdef2(bone_idx0, bone_idx1, weights[0])
                        else:
                            # BDEF4
                            bone_indices = []
                            for i in range(4):
                                if i < len(bone_names) and bone_names[i] in model.bones:
                                    bone_indices.append(list(model.bones.keys()).index(bone_names[i]))
                                else:
                                    bone_indices.append(0)
                            
                            bone_weights = weights[:4] + [0.0] * (4 - len(weights))
                            deform = Bdef4(
                                bone_indices[0], bone_indices[1], bone_indices[2], bone_indices[3],
                                bone_weights[0], bone_weights[1], bone_weights[2], bone_weights[3]
                            )
                    else:
                        # デフォルト
                        deform = Bdef1(0)

                    vertex = Vertex(
                        position=position,
                        normal=normal,
                        uv=uv,
                        extended_uvs=[],
                        deform=deform,
                        edge_factor=1.0
                    )
                    vertices.append(vertex)

                # 面データ変換
                faces = []
                for i in range(0, len(indices_values), 3):
                    idx_data0 = struct.unpack("<H", indices_values[i])[0]
                    idx_data1 = struct.unpack("<H", indices_values[i + 1])[0]
                    idx_data2 = struct.unpack("<H", indices_values[i + 2])[0]

                    # 面の向きを反転（右手座標系から左手座標系へ）
                    face_indices = [
                        vertex_idx + idx_data0,
                        vertex_idx + idx_data2,
                        vertex_idx + idx_data1
                    ]
                    faces.extend(face_indices)

                # マテリアル処理
                material_idx = primitive["material"]
                material_name = f"Material_{material_idx}"
                
                if material_idx < len(model.json_data["materials"]):
                    material_data = model.json_data["materials"][material_idx]
                    if "name" in material_data:
                        material_name = material_data["name"]

                # マテリアルが存在しない場合は作成
                if material_name not in model.materials:
                    material = Material(
                        name=material_name,
                        english_name=material_name,
                        diffuse_color=MVector4D(1, 1, 1, 1),
                        specular_color=MVector3D(0, 0, 0),
                        specular_factor=0,
                        ambient_color=MVector3D(0.2, 0.2, 0.2),
                        flag=0x01 | 0x02 | 0x04 | 0x08 | 0x10,
                        edge_color=MVector4D(0, 0, 0, 1),
                        edge_size=1,
                        texture_index=-1,
                        sphere_texture_index=-1,
                        sphere_mode=0,
                        toon_sharing_flag=0,
                        toon_texture_index=-1,
                        comment="",
                        vertex_count=len(faces) * 3
                    )
                    
                    # テクスチャ処理
                    if (material_idx < len(model.json_data["materials"]) and 
                        "pbrMetallicRoughness" in model.json_data["materials"][material_idx] and
                        "baseColorTexture" in model.json_data["materials"][material_idx]["pbrMetallicRoughness"]):
                        
                        texture_info = model.json_data["materials"][material_idx]["pbrMetallicRoughness"]["baseColorTexture"]
                        texture_idx = texture_info["index"]
                        
                        if texture_idx < len(model.json_data["textures"]):
                            texture_data = model.json_data["textures"][texture_idx]
                            image_idx = texture_data["source"]
                            
                            if image_idx < len(model.json_data["images"]):
                                image_data = model.json_data["images"][image_idx]
                                
                                # テクスチャファイルを保存
                                texture_name = self._save_texture(model, image_data, tex_dir_path)
                                if texture_name:
                                    texture_path = os.path.join("tex", texture_name)
                                    if texture_path not in model.textures:
                                        model.textures.append(texture_path)
                                    material.texture_index = model.textures.index(texture_path)
                    
                    model.materials[material_name] = material

                # 頂点とフェイスをモデルに追加
                for vertex in vertices:
                    vertex.index = len(model.vertex_dict)
                    model.vertex_dict[vertex.index] = vertex
                    if material_name not in model.vertices:
                        model.vertices[material_name] = []
                    model.vertices[material_name].append(vertex.index)

                # 面データをモデルに追加
                face_start_idx = len(model.indices)
                for i in range(0, len(faces), 3):
                    model.indices[face_start_idx + i // 3] = [faces[i], faces[i + 1], faces[i + 2]]

                vertex_idx += len(vertices)

        return model

    def _save_texture(self, model, image_data, tex_dir_path):
        """テクスチャファイルを保存"""
        try:
            if "uri" in image_data:
                # データURIの場合
                uri = image_data["uri"]
                if uri.startswith("data:"):
                    import base64
                    header, data = uri.split(",", 1)
                    mime_type = header.split(";")[0].split(":")[1]
                    
                    if mime_type in MIME_TYPE:
                        ext = MIME_TYPE[mime_type]
                        texture_name = f"texture_{len(model.textures)}.{ext}"
                        texture_path = os.path.join(tex_dir_path, texture_name)
                        
                        with open(texture_path, "wb") as f:
                            f.write(base64.b64decode(data))
                        
                        return texture_name
            elif "bufferView" in image_data:
                # バッファビューの場合
                buffer_view_idx = image_data["bufferView"]
                if buffer_view_idx < len(model.json_data["bufferViews"]):
                    buffer_view = model.json_data["bufferViews"][buffer_view_idx]
                    buffer_idx = buffer_view["buffer"]
                    
                    if buffer_idx in model.buffer_data:
                        buffer_data = model.buffer_data[buffer_idx]
                        start = buffer_view["byteOffset"]
                        length = buffer_view["byteLength"]
                        
                        mime_type = image_data.get("mimeType", "image/png")
                        if mime_type in MIME_TYPE:
                            ext = MIME_TYPE[mime_type]
                            texture_name = f"texture_{len(model.textures)}.{ext}"
                            texture_path = os.path.join(tex_dir_path, texture_name)
                            
                            with open(texture_path, "wb") as f:
                                f.write(buffer_data[start:start + length])
                            
                            return texture_name
        except Exception as e:
            logger.warning(f"テクスチャ保存エラー: {e}")
        
        return None
